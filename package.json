{"name": "mp3-audio-player", "version": "1.0.0", "description": "A professional, feature-rich MP3 audio player inspired by Sonaar MP3 Audio Player", "main": "index.html", "scripts": {"start": "npx http-server . -p 8080 -o", "dev": "npx live-server --port=8080 --open=index.html", "test": "npx http-server . -p 8080 -o test.html", "demo": "npx http-server . -p 8080 -o demo.html", "build": "echo 'No build process required - static files'", "lint": "npx eslint js/*.js", "format": "npx prettier --write *.html css/*.css js/*.js"}, "keywords": ["audio", "player", "mp3", "music", "waveform", "playlist", "web-audio", "html5", "javascript", "responsive", "son<PERSON>"], "author": "MP3 Audio Player Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/mp3-audio-player.git"}, "bugs": {"url": "https://github.com/your-username/mp3-audio-player/issues"}, "homepage": "https://github.com/your-username/mp3-audio-player#readme", "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.0.0", "http-server": "^14.0.0", "live-server": "^1.2.0"}, "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 60", "Firefox >= 55", "Safari >= 11", "Edge >= 79", "iOS >= 11", "Android >= 7"], "files": ["index.html", "demo.html", "css/", "js/", "assets/", "README.md", "LICENSE"]}