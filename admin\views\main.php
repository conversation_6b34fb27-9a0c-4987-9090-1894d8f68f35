<?php
/**
 * Main Admin Page
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$db = MP3_Audio_Player_Pro_Database::get_instance();
$playlists = $db->get_playlists(get_current_user_id());
$tracks = $db->get_tracks(get_current_user_id());
?>

<div class="wrap">
    <h1><?php _e('MP3 Audio Player Pro', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h1>
    
    <div class="mp3-player-admin-dashboard">
        <!-- Stats Cards -->
        <div class="mp3-player-stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="dashicons dashicons-playlist-audio"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo count($playlists); ?></h3>
                    <p><?php _e('Playlists', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="dashicons dashicons-format-audio"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo count($tracks); ?></h3>
                    <p><?php _e('Tracks', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="dashicons dashicons-admin-users"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo wp_count_posts('attachment')->inherit; ?></h3>
                    <p><?php _e('Media Files', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="mp3-player-quick-actions">
            <h2><?php _e('Quick Actions', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h2>
            
            <div class="action-buttons">
                <a href="<?php echo admin_url('media-new.php'); ?>" class="button button-primary">
                    <i class="dashicons dashicons-upload"></i>
                    <?php _e('Upload Audio Files', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                </a>
                
                <a href="<?php echo admin_url('admin.php?page=mp3-audio-player-playlists'); ?>" class="button button-secondary">
                    <i class="dashicons dashicons-playlist-audio"></i>
                    <?php _e('Manage Playlists', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                </a>
                
                <a href="<?php echo admin_url('admin.php?page=mp3-audio-player-settings'); ?>" class="button button-secondary">
                    <i class="dashicons dashicons-admin-settings"></i>
                    <?php _e('Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                </a>
            </div>
        </div>
        
        <!-- Recent Tracks -->
        <div class="mp3-player-recent-tracks">
            <h2><?php _e('Recent Tracks', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h2>
            
            <?php if (!empty($tracks)): ?>
                <div class="tracks-table-container">
                    <table class="wp-list-table widefat fixed striped">
                        <thead>
                            <tr>
                                <th><?php _e('Title', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                                <th><?php _e('Artist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                                <th><?php _e('Album', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                                <th><?php _e('Duration', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                                <th><?php _e('Date Added', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                                <th><?php _e('Actions', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($tracks, 0, 10) as $track): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo esc_html($track->title); ?></strong>
                                    </td>
                                    <td><?php echo esc_html($track->artist); ?></td>
                                    <td><?php echo esc_html($track->album); ?></td>
                                    <td><?php echo mp3_audio_player_pro_format_duration($track->duration); ?></td>
                                    <td><?php echo date_i18n(get_option('date_format'), strtotime($track->created_at)); ?></td>
                                    <td>
                                        <a href="<?php echo esc_url($track->file_url); ?>" target="_blank" class="button button-small">
                                            <?php _e('Play', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                                        </a>
                                        <button type="button" class="button button-small button-link-delete" 
                                                onclick="deleteTrack(<?php echo $track->id; ?>)">
                                            <?php _e('Delete', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <?php if (count($tracks) > 10): ?>
                    <p>
                        <a href="<?php echo admin_url('admin.php?page=mp3-audio-player-playlists'); ?>">
                            <?php printf(__('View all %d tracks', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), count($tracks)); ?>
                        </a>
                    </p>
                <?php endif; ?>
            <?php else: ?>
                <div class="mp3-player-empty-state">
                    <i class="dashicons dashicons-format-audio"></i>
                    <h3><?php _e('No tracks found', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h3>
                    <p><?php _e('Upload some audio files to get started.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                    <a href="<?php echo admin_url('media-new.php'); ?>" class="button button-primary">
                        <?php _e('Upload Audio Files', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Shortcode Generator -->
        <div class="mp3-player-shortcode-generator">
            <h2><?php _e('Shortcode Generator', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h2>
            
            <div class="shortcode-form">
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <select id="shortcode-playlist">
                                <option value=""><?php _e('All Tracks', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                                <?php foreach ($playlists as $playlist): ?>
                                    <option value="<?php echo esc_attr($playlist->id); ?>">
                                        <?php echo esc_html($playlist->name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Layout', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <select id="shortcode-layout">
                                <option value="default"><?php _e('Default', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                                <option value="compact"><?php _e('Compact', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                                <option value="mini"><?php _e('Mini', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Theme', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <select id="shortcode-theme">
                                <option value="light"><?php _e('Light', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                                <option value="dark"><?php _e('Dark', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Options', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" id="shortcode-waveform" checked>
                                <?php _e('Show Waveform', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" id="shortcode-playlist" checked>
                                <?php _e('Show Playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </label><br>
                            <label>
                                <input type="checkbox" id="shortcode-sticky" checked>
                                <?php _e('Sticky Player', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </label>
                        </td>
                    </tr>
                </table>
                
                <div class="shortcode-output">
                    <label for="generated-shortcode"><?php _e('Generated Shortcode:', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></label>
                    <input type="text" id="generated-shortcode" class="large-text code" readonly 
                           value="[mp3_audio_player]">
                    <button type="button" class="button" onclick="copyShortcode()">
                        <?php _e('Copy', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.mp3-player-admin-dashboard {
    max-width: 1200px;
}

.mp3-player-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    font-size: 24px;
    color: #0073aa;
}

.stat-content h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.stat-content p {
    margin: 0;
    color: #666;
}

.mp3-player-quick-actions,
.mp3-player-recent-tracks,
.mp3-player-shortcode-generator {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-buttons .button {
    display: flex;
    align-items: center;
    gap: 5px;
}

.mp3-player-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.mp3-player-empty-state .dashicons {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.shortcode-output {
    margin-top: 20px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.shortcode-output input {
    margin: 10px 0;
}
</style>

<script>
function updateShortcode() {
    var playlist = document.getElementById('shortcode-playlist').value;
    var layout = document.getElementById('shortcode-layout').value;
    var theme = document.getElementById('shortcode-theme').value;
    var showWaveform = document.getElementById('shortcode-waveform').checked;
    var showPlaylist = document.getElementById('shortcode-playlist').checked;
    var stickyPlayer = document.getElementById('shortcode-sticky').checked;
    
    var shortcode = '[mp3_audio_player';
    
    if (playlist) shortcode += ' playlist="' + playlist + '"';
    if (layout !== 'default') shortcode += ' layout="' + layout + '"';
    if (theme !== 'light') shortcode += ' theme="' + theme + '"';
    if (!showWaveform) shortcode += ' show_waveform="false"';
    if (!showPlaylist) shortcode += ' show_playlist="false"';
    if (!stickyPlayer) shortcode += ' sticky_player="false"';
    
    shortcode += ']';
    
    document.getElementById('generated-shortcode').value = shortcode;
}

function copyShortcode() {
    var shortcodeInput = document.getElementById('generated-shortcode');
    shortcodeInput.select();
    document.execCommand('copy');
    
    // Show feedback
    var button = event.target;
    var originalText = button.textContent;
    button.textContent = '<?php _e('Copied!', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>';
    setTimeout(function() {
        button.textContent = originalText;
    }, 2000);
}

function deleteTrack(trackId) {
    if (!confirm('<?php _e('Are you sure you want to delete this track?', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>')) {
        return;
    }
    
    // AJAX call to delete track
    var data = {
        action: 'mp3_player_delete_track',
        track_id: trackId,
        nonce: '<?php echo wp_create_nonce('mp3_player_admin_nonce'); ?>'
    };
    
    jQuery.post(ajaxurl, data, function(response) {
        if (response.success) {
            location.reload();
        } else {
            alert(response.data || '<?php _e('Failed to delete track.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>');
        }
    });
}

// Update shortcode when options change
document.addEventListener('DOMContentLoaded', function() {
    var inputs = ['shortcode-playlist', 'shortcode-layout', 'shortcode-theme', 'shortcode-waveform', 'shortcode-playlist', 'shortcode-sticky'];
    inputs.forEach(function(id) {
        var element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', updateShortcode);
        }
    });
    
    updateShortcode();
});
</script>
