<?php
/**
 * Shortcode Handler Class
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * MP3 Audio Player Pro Shortcode Class
 */
class MP3_Audio_Player_Pro_Shortcode {
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Player counter for unique IDs
     */
    private static $player_count = 0;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Constructor is private to prevent direct instantiation
    }
    
    /**
     * Render shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    public function render($atts) {
        // Parse shortcode attributes
        $atts = shortcode_atts(array(
            'playlist' => '',
            'tracks' => '',
            'theme' => 'light',
            'width' => '100%',
            'height' => 'auto',
            'show_waveform' => 'true',
            'show_playlist' => 'true',
            'sticky_player' => 'true',
            'autoplay' => 'false',
            'volume' => '1',
            'layout' => 'default', // default, compact, mini
            'color_scheme' => '',
            'custom_css' => '',
        ), $atts, 'mp3_audio_player');
        
        // Increment player counter for unique IDs
        self::$player_count++;
        $player_id = 'mp3-player-' . self::$player_count;
        
        // Get tracks data
        $tracks_data = $this->get_tracks_data($atts);
        
        if (empty($tracks_data)) {
            return '<div class="mp3-player-error">' . 
                   __('No tracks found. Please check your playlist or track IDs.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN) . 
                   '</div>';
        }
        
        // Generate player HTML
        $html = $this->generate_player_html($player_id, $atts, $tracks_data);
        
        // Add inline styles if needed
        if (!empty($atts['custom_css'])) {
            $html .= '<style>' . wp_strip_all_tags($atts['custom_css']) . '</style>';
        }
        
        // Add inline JavaScript for player initialization
        $html .= $this->generate_player_script($player_id, $atts, $tracks_data);
        
        return $html;
    }
    
    /**
     * Get tracks data based on shortcode attributes
     */
    private function get_tracks_data($atts) {
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        $tracks = array();
        
        if (!empty($atts['playlist'])) {
            // Get tracks from playlist
            $playlist_id = intval($atts['playlist']);
            $tracks = $db->get_tracks(null, $playlist_id);
        } elseif (!empty($atts['tracks'])) {
            // Get specific tracks by IDs
            $track_ids = array_map('intval', explode(',', $atts['tracks']));
            foreach ($track_ids as $track_id) {
                $track = $db->get_track($track_id);
                if ($track) {
                    $tracks[] = $track;
                }
            }
        } else {
            // Get all public tracks or user's tracks
            $user_id = is_user_logged_in() ? get_current_user_id() : null;
            $tracks = $db->get_tracks($user_id);
        }
        
        // Convert to frontend format
        return $this->format_tracks_for_frontend($tracks);
    }
    
    /**
     * Format tracks for frontend consumption
     */
    private function format_tracks_for_frontend($tracks) {
        $formatted_tracks = array();
        
        foreach ($tracks as $track) {
            $metadata = !empty($track->metadata) ? unserialize($track->metadata) : array();
            
            $formatted_tracks[] = array(
                'id' => $track->id,
                'title' => $track->title,
                'artist' => $track->artist,
                'album' => $track->album,
                'duration' => intval($track->duration),
                'src' => $track->file_url,
                'artwork' => $track->artwork_url,
                'genre' => $track->genre,
                'year' => $track->year,
                'trackNumber' => $track->track_number,
                'fileSize' => intval($track->file_size),
                'format' => $track->file_format,
                'bitrate' => $track->bitrate,
                'sampleRate' => $track->sample_rate,
                'metadata' => $metadata,
            );
        }
        
        return $formatted_tracks;
    }
    
    /**
     * Generate player HTML
     */
    private function generate_player_html($player_id, $atts, $tracks_data) {
        $layout = $atts['layout'];
        $show_waveform = filter_var($atts['show_waveform'], FILTER_VALIDATE_BOOLEAN);
        $show_playlist = filter_var($atts['show_playlist'], FILTER_VALIDATE_BOOLEAN);
        
        ob_start();
        ?>
        <div id="<?php echo esc_attr($player_id); ?>" 
             class="mp3-audio-player-container layout-<?php echo esc_attr($layout); ?> theme-<?php echo esc_attr($atts['theme']); ?>"
             data-theme="<?php echo esc_attr($atts['theme']); ?>"
             style="width: <?php echo esc_attr($atts['width']); ?>; height: <?php echo esc_attr($atts['height']); ?>;">
            
            <?php if ($layout === 'mini'): ?>
                <?php echo $this->render_mini_player($player_id, $atts, $tracks_data); ?>
            <?php elseif ($layout === 'compact'): ?>
                <?php echo $this->render_compact_player($player_id, $atts, $tracks_data); ?>
            <?php else: ?>
                <?php echo $this->render_default_player($player_id, $atts, $tracks_data, $show_waveform, $show_playlist); ?>
            <?php endif; ?>
            
            <!-- Audio Element -->
            <audio id="<?php echo esc_attr($player_id); ?>-audio" preload="metadata"></audio>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render default player layout
     */
    private function render_default_player($player_id, $atts, $tracks_data, $show_waveform, $show_playlist) {
        ob_start();
        ?>
        <div class="player-section">
            <div class="player-container">
                <!-- Album Art -->
                <div class="album-art-container">
                    <div class="album-art">
                        <img id="<?php echo esc_attr($player_id); ?>-album-art" 
                             src="<?php echo MP3_AUDIO_PLAYER_PRO_PLUGIN_URL; ?>assets/images/default-album.jpg" 
                             alt="Album Art">
                        <div class="album-art-overlay">
                            <button class="play-btn-large" id="<?php echo esc_attr($player_id); ?>-main-play-btn">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Track Info -->
                <div class="track-info">
                    <h2 class="track-title" id="<?php echo esc_attr($player_id); ?>-track-title">
                        <?php _e('Select a track to play', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                    </h2>
                    <p class="track-artist" id="<?php echo esc_attr($player_id); ?>-track-artist">
                        <?php _e('Unknown Artist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                    </p>
                    <p class="track-album" id="<?php echo esc_attr($player_id); ?>-track-album">
                        <?php _e('Unknown Album', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                    </p>
                </div>

                <?php if ($show_waveform): ?>
                <!-- Waveform Container -->
                <div class="waveform-container">
                    <canvas id="<?php echo esc_attr($player_id); ?>-waveform-canvas" class="waveform-canvas"></canvas>
                    <div class="waveform-progress" id="<?php echo esc_attr($player_id); ?>-waveform-progress"></div>
                </div>
                <?php endif; ?>

                <!-- Player Controls -->
                <div class="player-controls">
                    <div class="control-buttons">
                        <button class="control-btn" id="<?php echo esc_attr($player_id); ?>-shuffle-btn">
                            <i class="fas fa-random"></i>
                        </button>
                        <button class="control-btn" id="<?php echo esc_attr($player_id); ?>-prev-btn">
                            <i class="fas fa-step-backward"></i>
                        </button>
                        <button class="control-btn play-pause-btn" id="<?php echo esc_attr($player_id); ?>-play-pause-btn">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="control-btn" id="<?php echo esc_attr($player_id); ?>-next-btn">
                            <i class="fas fa-step-forward"></i>
                        </button>
                        <button class="control-btn" id="<?php echo esc_attr($player_id); ?>-repeat-btn">
                            <i class="fas fa-redo"></i>
                        </button>
                    </div>

                    <!-- Progress Bar -->
                    <div class="progress-container">
                        <span class="time-display" id="<?php echo esc_attr($player_id); ?>-current-time">0:00</span>
                        <div class="progress-bar" id="<?php echo esc_attr($player_id); ?>-progress-bar">
                            <div class="progress-fill" id="<?php echo esc_attr($player_id); ?>-progress-fill"></div>
                            <div class="progress-handle" id="<?php echo esc_attr($player_id); ?>-progress-handle"></div>
                        </div>
                        <span class="time-display" id="<?php echo esc_attr($player_id); ?>-total-time">0:00</span>
                    </div>

                    <!-- Volume Control -->
                    <div class="volume-container">
                        <button class="control-btn" id="<?php echo esc_attr($player_id); ?>-mute-btn">
                            <i class="fas fa-volume-up"></i>
                        </button>
                        <div class="volume-bar" id="<?php echo esc_attr($player_id); ?>-volume-bar">
                            <div class="volume-fill" id="<?php echo esc_attr($player_id); ?>-volume-fill"></div>
                            <div class="volume-handle" id="<?php echo esc_attr($player_id); ?>-volume-handle"></div>
                        </div>
                    </div>

                    <!-- Additional Controls -->
                    <div class="additional-controls">
                        <button class="control-btn" id="<?php echo esc_attr($player_id); ?>-speed-btn" title="<?php _e('Playback Speed', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>">
                            <span class="speed-text">1x</span>
                        </button>
                        <button class="control-btn" id="<?php echo esc_attr($player_id); ?>-download-btn" title="<?php _e('Download', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="control-btn" id="<?php echo esc_attr($player_id); ?>-share-btn" title="<?php _e('Share', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>">
                            <i class="fas fa-share"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($show_playlist): ?>
        <!-- Playlist Section -->
        <div class="playlist-section">
            <div class="playlist-header">
                <h3><?php _e('Playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h3>
                <div class="playlist-controls">
                    <button class="btn btn-small" id="<?php echo esc_attr($player_id); ?>-clear-playlist-btn">
                        <i class="fas fa-trash"></i>
                        <?php _e('Clear', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                    </button>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="search-filter-container">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="<?php echo esc_attr($player_id); ?>-search-input" 
                           placeholder="<?php _e('Search tracks...', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>">
                </div>
                <div class="filter-controls">
                    <select id="<?php echo esc_attr($player_id); ?>-sort-by">
                        <option value="title"><?php _e('Sort by Title', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                        <option value="artist"><?php _e('Sort by Artist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                        <option value="album"><?php _e('Sort by Album', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                        <option value="duration"><?php _e('Sort by Duration', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                    </select>
                </div>
            </div>

            <!-- Playlist Container -->
            <div class="playlist-container" id="<?php echo esc_attr($player_id); ?>-playlist-container">
                <!-- Playlist items will be populated by JavaScript -->
            </div>
        </div>
        <?php endif; ?>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render compact player layout
     */
    private function render_compact_player($player_id, $atts, $tracks_data) {
        ob_start();
        ?>
        <div class="compact-player">
            <div class="compact-track-info">
                <img id="<?php echo esc_attr($player_id); ?>-album-art" 
                     src="<?php echo MP3_AUDIO_PLAYER_PRO_PLUGIN_URL; ?>assets/images/default-album.jpg" 
                     alt="Album Art" class="compact-album-art">
                <div class="compact-text-info">
                    <span class="compact-title" id="<?php echo esc_attr($player_id); ?>-track-title">
                        <?php _e('Select a track', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                    </span>
                    <span class="compact-artist" id="<?php echo esc_attr($player_id); ?>-track-artist">
                        <?php _e('Unknown Artist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                    </span>
                </div>
            </div>
            
            <div class="compact-controls">
                <button class="compact-btn" id="<?php echo esc_attr($player_id); ?>-prev-btn">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button class="compact-btn compact-play-btn" id="<?php echo esc_attr($player_id); ?>-play-pause-btn">
                    <i class="fas fa-play"></i>
                </button>
                <button class="compact-btn" id="<?php echo esc_attr($player_id); ?>-next-btn">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>

            <div class="compact-progress">
                <div class="compact-progress-bar" id="<?php echo esc_attr($player_id); ?>-progress-bar">
                    <div class="compact-progress-fill" id="<?php echo esc_attr($player_id); ?>-progress-fill"></div>
                </div>
            </div>

            <div class="compact-volume">
                <button class="compact-btn" id="<?php echo esc_attr($player_id); ?>-mute-btn">
                    <i class="fas fa-volume-up"></i>
                </button>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render mini player layout
     */
    private function render_mini_player($player_id, $atts, $tracks_data) {
        ob_start();
        ?>
        <div class="mini-player">
            <button class="mini-play-btn" id="<?php echo esc_attr($player_id); ?>-play-pause-btn">
                <i class="fas fa-play"></i>
            </button>
            <div class="mini-track-info">
                <span id="<?php echo esc_attr($player_id); ?>-track-title">
                    <?php _e('Click to play', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                </span>
            </div>
            <div class="mini-progress">
                <div class="mini-progress-bar" id="<?php echo esc_attr($player_id); ?>-progress-bar">
                    <div class="mini-progress-fill" id="<?php echo esc_attr($player_id); ?>-progress-fill"></div>
                </div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Generate player initialization script
     */
    private function generate_player_script($player_id, $atts, $tracks_data) {
        $config = array(
            'playerId' => $player_id,
            'tracks' => $tracks_data,
            'autoplay' => filter_var($atts['autoplay'], FILTER_VALIDATE_BOOLEAN),
            'volume' => floatval($atts['volume']),
            'theme' => $atts['theme'],
            'layout' => $atts['layout'],
            'showWaveform' => filter_var($atts['show_waveform'], FILTER_VALIDATE_BOOLEAN),
            'showPlaylist' => filter_var($atts['show_playlist'], FILTER_VALIDATE_BOOLEAN),
            'stickyPlayer' => filter_var($atts['sticky_player'], FILTER_VALIDATE_BOOLEAN),
            'colorScheme' => $atts['color_scheme'],
        );
        
        ob_start();
        ?>
        <script type="text/javascript">
        (function() {
            // Wait for DOM and scripts to load
            function initPlayer() {
                if (typeof MP3AudioPlayer !== 'undefined') {
                    var config = <?php echo json_encode($config); ?>;
                    var player = new MP3AudioPlayer(config);
                    
                    // Store player instance globally for debugging
                    window['mp3Player_' + config.playerId] = player;
                } else {
                    // Retry after a short delay
                    setTimeout(initPlayer, 100);
                }
            }
            
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initPlayer);
            } else {
                initPlayer();
            }
        })();
        </script>
        <?php
        return ob_get_clean();
    }
}
