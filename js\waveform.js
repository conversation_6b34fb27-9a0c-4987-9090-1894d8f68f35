/**
 * Waveform Visualizer - Creates interactive waveform displays
 * Handles waveform generation, rendering, and user interaction
 */

class WaveformVisualizer {
    constructor(audioEngine) {
        this.audioEngine = audioEngine;
        this.canvas = document.getElementById('waveformCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.progressElement = document.getElementById('waveformProgress');
        
        // Waveform data
        this.waveformData = null;
        this.peaks = [];
        this.isGenerating = false;
        
        // Visual settings
        this.barWidth = 2;
        this.barGap = 1;
        this.barCount = 0;
        this.colors = {
            background: '#f1f5f9',
            waveform: '#cbd5e1',
            progress: '#6366f1',
            hover: '#8b5cf6'
        };
        
        // Interaction
        this.isHovering = false;
        this.hoverPosition = 0;
        this.isDragging = false;
        
        this.initializeCanvas();
        this.bindEvents();
        this.updateColors();
    }

    initializeCanvas() {
        this.resizeCanvas();
        this.drawEmptyWaveform();
    }

    resizeCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        
        // Set canvas size with device pixel ratio for crisp rendering
        const dpr = window.devicePixelRatio || 1;
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        // Scale context for high DPI displays
        this.ctx.scale(dpr, dpr);
        
        // Calculate bar count based on canvas width
        this.barCount = Math.floor(rect.width / (this.barWidth + this.barGap));
    }

    bindEvents() {
        // Canvas interaction
        this.canvas.addEventListener('click', (e) => this.handleClick(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        this.canvas.addEventListener('mouseenter', () => this.handleMouseEnter());
        this.canvas.addEventListener('mouseleave', () => this.handleMouseLeave());
        this.canvas.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        this.canvas.addEventListener('mouseup', () => this.handleMouseUp());
        
        // Window resize
        window.addEventListener('resize', () => {
            this.resizeCanvas();
            this.redraw();
        });
        
        // Audio engine events
        document.addEventListener('audioEngine:trackLoaded', (e) => {
            this.generateWaveform(e.detail.track);
        });
        
        document.addEventListener('audioEngine:timeUpdate', (e) => {
            this.updateProgress(e.detail.progress);
        });
        
        // Theme changes
        document.addEventListener('themeChanged', () => {
            this.updateColors();
            this.redraw();
        });
    }

    // Waveform generation
    async generateWaveform(track) {
        if (!track || !track.src || this.isGenerating) return;
        
        this.isGenerating = true;
        this.drawLoadingState();
        
        try {
            const audioBuffer = await this.loadAudioBuffer(track.src);
            this.peaks = this.extractPeaks(audioBuffer, this.barCount);
            this.redraw();
        } catch (error) {
            console.error('Failed to generate waveform:', error);
            this.drawErrorState();
        } finally {
            this.isGenerating = false;
        }
    }

    async loadAudioBuffer(url) {
        const response = await fetch(url);
        const arrayBuffer = await response.arrayBuffer();
        
        // Use audio engine's context if available, otherwise create temporary one
        const audioContext = this.audioEngine.audioContext || new (window.AudioContext || window.webkitAudioContext)();
        return await audioContext.decodeAudioData(arrayBuffer);
    }

    extractPeaks(audioBuffer, peakCount) {
        const channelData = audioBuffer.getChannelData(0); // Use first channel
        const sampleSize = Math.floor(channelData.length / peakCount);
        const peaks = [];
        
        for (let i = 0; i < peakCount; i++) {
            const start = i * sampleSize;
            const end = Math.min(start + sampleSize, channelData.length);
            
            let peak = 0;
            for (let j = start; j < end; j++) {
                const sample = Math.abs(channelData[j]);
                if (sample > peak) {
                    peak = sample;
                }
            }
            
            peaks.push(peak);
        }
        
        // Normalize peaks
        const maxPeak = Math.max(...peaks);
        if (maxPeak > 0) {
            return peaks.map(peak => peak / maxPeak);
        }
        
        return peaks;
    }

    // Drawing methods
    redraw() {
        this.clearCanvas();
        
        if (this.peaks.length === 0) {
            this.drawEmptyWaveform();
            return;
        }
        
        this.drawWaveform();
        this.drawProgress();
        
        if (this.isHovering) {
            this.drawHoverIndicator();
        }
    }

    clearCanvas() {
        const rect = this.canvas.getBoundingClientRect();
        this.ctx.clearRect(0, 0, rect.width, rect.height);
    }

    drawEmptyWaveform() {
        const rect = this.canvas.getBoundingClientRect();
        const centerY = rect.height / 2;
        const barHeight = 4;
        
        this.ctx.fillStyle = this.colors.waveform;
        
        for (let i = 0; i < this.barCount; i++) {
            const x = i * (this.barWidth + this.barGap);
            const y = centerY - barHeight / 2;
            
            this.ctx.fillRect(x, y, this.barWidth, barHeight);
        }
    }

    drawWaveform() {
        const rect = this.canvas.getBoundingClientRect();
        const centerY = rect.height / 2;
        const maxBarHeight = rect.height * 0.8;
        
        this.ctx.fillStyle = this.colors.waveform;
        
        for (let i = 0; i < this.peaks.length && i < this.barCount; i++) {
            const peak = this.peaks[i];
            const barHeight = Math.max(2, peak * maxBarHeight);
            const x = i * (this.barWidth + this.barGap);
            const y = centerY - barHeight / 2;
            
            this.ctx.fillRect(x, y, this.barWidth, barHeight);
        }
    }

    drawProgress() {
        if (!this.audioEngine.getCurrentTrack()) return;
        
        const currentTime = this.audioEngine.getCurrentTime();
        const duration = this.audioEngine.getDuration();
        
        if (duration === 0) return;
        
        const progress = currentTime / duration;
        const rect = this.canvas.getBoundingClientRect();
        const progressWidth = progress * rect.width;
        
        // Update CSS progress element
        this.progressElement.style.width = `${progress * 100}%`;
        
        // Draw progress bars
        const centerY = rect.height / 2;
        const maxBarHeight = rect.height * 0.8;
        
        this.ctx.fillStyle = this.colors.progress;
        
        for (let i = 0; i < this.peaks.length && i < this.barCount; i++) {
            const x = i * (this.barWidth + this.barGap);
            
            if (x >= progressWidth) break;
            
            const peak = this.peaks[i];
            const barHeight = Math.max(2, peak * maxBarHeight);
            const y = centerY - barHeight / 2;
            
            this.ctx.fillRect(x, y, this.barWidth, barHeight);
        }
    }

    drawHoverIndicator() {
        const rect = this.canvas.getBoundingClientRect();
        
        this.ctx.strokeStyle = this.colors.hover;
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(this.hoverPosition, 0);
        this.ctx.lineTo(this.hoverPosition, rect.height);
        this.ctx.stroke();
    }

    drawLoadingState() {
        this.clearCanvas();
        
        const rect = this.canvas.getBoundingClientRect();
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        this.ctx.fillStyle = this.colors.waveform;
        this.ctx.font = '14px Inter, sans-serif';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Generating waveform...', centerX, centerY);
    }

    drawErrorState() {
        this.clearCanvas();
        
        const rect = this.canvas.getBoundingClientRect();
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        this.ctx.fillStyle = this.colors.waveform;
        this.ctx.font = '14px Inter, sans-serif';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Unable to generate waveform', centerX, centerY);
        
        this.drawEmptyWaveform();
    }

    // Interaction handlers
    handleClick(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const progress = x / rect.width;
        
        this.seekToProgress(progress);
    }

    handleMouseMove(event) {
        const rect = this.canvas.getBoundingClientRect();
        this.hoverPosition = event.clientX - rect.left;
        
        if (this.isDragging) {
            const progress = this.hoverPosition / rect.width;
            this.seekToProgress(progress);
        }
        
        if (this.isHovering) {
            this.redraw();
        }
    }

    handleMouseEnter() {
        this.isHovering = true;
        this.canvas.style.cursor = 'pointer';
    }

    handleMouseLeave() {
        this.isHovering = false;
        this.isDragging = false;
        this.canvas.style.cursor = 'default';
        this.redraw();
    }

    handleMouseDown(event) {
        this.isDragging = true;
        this.handleClick(event);
    }

    handleMouseUp() {
        this.isDragging = false;
    }

    // Utility methods
    seekToProgress(progress) {
        const clampedProgress = Math.max(0, Math.min(1, progress));
        this.audioEngine.seekToPercent(clampedProgress * 100);
    }

    updateProgress(progress) {
        if (!this.isDragging) {
            this.redraw();
        }
    }

    updateColors() {
        // Get CSS custom properties for theming
        const root = document.documentElement;
        const computedStyle = getComputedStyle(root);
        
        this.colors = {
            background: computedStyle.getPropertyValue('--bg-tertiary').trim() || '#f1f5f9',
            waveform: computedStyle.getPropertyValue('--border-color').trim() || '#cbd5e1',
            progress: computedStyle.getPropertyValue('--primary-color').trim() || '#6366f1',
            hover: computedStyle.getPropertyValue('--primary-hover').trim() || '#8b5cf6'
        };
    }

    // Animation methods
    animateWaveform() {
        if (!this.audioEngine.isPlayingState()) return;
        
        const frequencyData = this.audioEngine.getFrequencyData();
        if (!frequencyData) return;
        
        // Create animated bars based on frequency data
        const rect = this.canvas.getBoundingClientRect();
        const centerY = rect.height / 2;
        const maxBarHeight = rect.height * 0.3;
        
        this.ctx.fillStyle = this.colors.progress;
        
        // Draw frequency bars
        const barCount = Math.min(frequencyData.length / 4, this.barCount);
        for (let i = 0; i < barCount; i++) {
            const frequency = frequencyData[i * 4] / 255; // Normalize to 0-1
            const barHeight = frequency * maxBarHeight;
            const x = i * (this.barWidth + this.barGap);
            const y = centerY - barHeight / 2;
            
            this.ctx.globalAlpha = 0.3;
            this.ctx.fillRect(x, y, this.barWidth, barHeight);
        }
        
        this.ctx.globalAlpha = 1;
    }

    // Public methods
    setColors(colors) {
        this.colors = { ...this.colors, ...colors };
        this.redraw();
    }

    regenerateWaveform() {
        const currentTrack = this.audioEngine.getCurrentTrack();
        if (currentTrack) {
            this.generateWaveform(currentTrack);
        }
    }

    destroy() {
        // Clean up event listeners and resources
        this.peaks = [];
        this.waveformData = null;
    }
}

// Export for use in other modules
window.WaveformVisualizer = WaveformVisualizer;
