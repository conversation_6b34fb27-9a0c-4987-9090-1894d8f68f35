/**
 * UI Controller - Manages user interface interactions and updates
 * Handles all UI elements, controls, and visual feedback
 */

class UIController {
    constructor(audioEngine, playlistManager) {
        this.audioEngine = audioEngine;
        this.playlistManager = playlistManager;
        
        // Main UI elements
        this.albumArt = document.getElementById('albumArt');
        this.trackTitle = document.getElementById('trackTitle');
        this.trackArtist = document.getElementById('trackArtist');
        this.trackAlbum = document.getElementById('trackAlbum');
        
        // Control elements
        this.mainPlayBtn = document.getElementById('mainPlayBtn');
        this.playPauseBtn = document.getElementById('playPauseBtn');
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');
        this.shuffleBtn = document.getElementById('shuffleBtn');
        this.repeatBtn = document.getElementById('repeatBtn');
        
        // Progress elements
        this.progressBar = document.getElementById('progressBar');
        this.progressFill = document.getElementById('progressFill');
        this.progressHandle = document.getElementById('progressHandle');
        this.currentTime = document.getElementById('currentTime');
        this.totalTime = document.getElementById('totalTime');
        
        // Volume elements
        this.muteBtn = document.getElementById('muteBtn');
        this.volumeBar = document.getElementById('volumeBar');
        this.volumeFill = document.getElementById('volumeFill');
        this.volumeHandle = document.getElementById('volumeHandle');
        
        // Additional controls
        this.speedBtn = document.getElementById('speedBtn');
        this.downloadBtn = document.getElementById('downloadBtn');
        this.shareBtn = document.getElementById('shareBtn');
        this.themeToggle = document.getElementById('themeToggle');
        
        // Sticky player elements
        this.stickyPlayer = document.getElementById('stickyPlayer');
        this.stickyAlbumArt = document.getElementById('stickyAlbumArt');
        this.stickyTitle = document.getElementById('stickyTitle');
        this.stickyArtist = document.getElementById('stickyArtist');
        this.stickyPlayBtn = document.getElementById('stickyPlayBtn');
        this.stickyPrevBtn = document.getElementById('stickyPrevBtn');
        this.stickyNextBtn = document.getElementById('stickyNextBtn');
        this.stickyProgressBar = document.getElementById('stickyProgressBar');
        this.stickyProgressFill = document.getElementById('stickyProgressFill');
        this.stickyMuteBtn = document.getElementById('stickyMuteBtn');
        this.stickyVolumeBar = document.getElementById('stickyVolumeBar');
        this.stickyVolumeFill = document.getElementById('stickyVolumeFill');
        
        // State
        this.isDraggingProgress = false;
        this.isDraggingVolume = false;
        this.currentTheme = localStorage.getItem('mp3Player_theme') || 'light';
        this.stickyPlayerVisible = false;
        
        this.initializeUI();
        this.bindEvents();
        this.applyTheme();
    }

    initializeUI() {
        // Set initial states
        this.updatePlayButton(false);
        this.updateShuffleButton(false);
        this.updateRepeatButton('none');
        this.updateVolumeDisplay(1);
        this.updateSpeedDisplay(1);
        
        // Show sticky player if there's a current track
        this.updateStickyPlayerVisibility();
    }

    bindEvents() {
        // Main play button
        this.mainPlayBtn.addEventListener('click', () => this.togglePlayback());
        this.playPauseBtn.addEventListener('click', () => this.togglePlayback());
        
        // Navigation
        this.prevBtn.addEventListener('click', () => this.audioEngine.previous());
        this.nextBtn.addEventListener('click', () => this.audioEngine.next());
        
        // Shuffle and repeat
        this.shuffleBtn.addEventListener('click', () => this.toggleShuffle());
        this.repeatBtn.addEventListener('click', () => this.toggleRepeat());
        
        // Progress bar
        this.bindProgressEvents(this.progressBar, this.progressFill, this.progressHandle);
        this.bindProgressEvents(this.stickyProgressBar, this.stickyProgressFill);
        
        // Volume control
        this.muteBtn.addEventListener('click', () => this.toggleMute());
        this.stickyMuteBtn.addEventListener('click', () => this.toggleMute());
        this.bindVolumeEvents(this.volumeBar, this.volumeFill, this.volumeHandle);
        this.bindVolumeEvents(this.stickyVolumeBar, this.stickyVolumeFill);
        
        // Additional controls
        this.speedBtn.addEventListener('click', () => this.cyclePlaybackSpeed());
        this.downloadBtn.addEventListener('click', () => this.downloadCurrentTrack());
        this.shareBtn.addEventListener('click', () => this.shareCurrentTrack());
        this.themeToggle.addEventListener('click', () => this.toggleTheme());
        
        // Sticky player controls
        this.stickyPlayBtn.addEventListener('click', () => this.togglePlayback());
        this.stickyPrevBtn.addEventListener('click', () => this.audioEngine.previous());
        this.stickyNextBtn.addEventListener('click', () => this.audioEngine.next());
        
        // Audio engine events
        this.bindAudioEngineEvents();
        
        // Keyboard shortcuts
        this.bindKeyboardEvents();
        
        // Window events
        window.addEventListener('scroll', () => this.handleScroll());
        window.addEventListener('beforeunload', () => this.saveState());
    }

    bindProgressEvents(progressBar, progressFill, progressHandle = null) {
        const handleProgressInteraction = (event) => {
            const rect = progressBar.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((event.clientX - rect.left) / rect.width) * 100));
            this.audioEngine.seekToPercent(percent);
        };
        
        progressBar.addEventListener('click', handleProgressInteraction);
        
        if (progressHandle) {
            let isDragging = false;
            
            progressHandle.addEventListener('mousedown', (e) => {
                isDragging = true;
                this.isDraggingProgress = true;
                e.preventDefault();
            });
            
            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    handleProgressInteraction(e);
                }
            });
            
            document.addEventListener('mouseup', () => {
                isDragging = false;
                this.isDraggingProgress = false;
            });
        }
    }

    bindVolumeEvents(volumeBar, volumeFill, volumeHandle = null) {
        const handleVolumeInteraction = (event) => {
            const rect = volumeBar.getBoundingClientRect();
            const percent = Math.max(0, Math.min(100, ((event.clientX - rect.left) / rect.width) * 100));
            this.audioEngine.setVolume(percent / 100);
        };
        
        volumeBar.addEventListener('click', handleVolumeInteraction);
        
        if (volumeHandle) {
            let isDragging = false;
            
            volumeHandle.addEventListener('mousedown', (e) => {
                isDragging = true;
                this.isDraggingVolume = true;
                e.preventDefault();
            });
            
            document.addEventListener('mousemove', (e) => {
                if (isDragging) {
                    handleVolumeInteraction(e);
                }
            });
            
            document.addEventListener('mouseup', () => {
                isDragging = false;
                this.isDraggingVolume = false;
            });
        }
    }

    bindAudioEngineEvents() {
        document.addEventListener('audioEngine:trackLoaded', (e) => {
            this.updateTrackInfo(e.detail.track);
            this.updateStickyPlayerVisibility();
        });
        
        document.addEventListener('audioEngine:play', () => {
            this.updatePlayButton(true);
        });
        
        document.addEventListener('audioEngine:pause', () => {
            this.updatePlayButton(false);
        });
        
        document.addEventListener('audioEngine:timeUpdate', (e) => {
            this.updateProgress(e.detail);
        });
        
        document.addEventListener('audioEngine:metadataLoaded', (e) => {
            this.updateDuration(e.detail.duration);
        });
        
        document.addEventListener('audioEngine:volumeChange', (e) => {
            this.updateVolumeDisplay(e.detail.volume, e.detail.muted);
        });
        
        document.addEventListener('audioEngine:playbackRateChanged', (e) => {
            this.updateSpeedDisplay(e.detail.rate);
        });
        
        document.addEventListener('audioEngine:repeatModeChanged', (e) => {
            this.updateRepeatButton(e.detail.mode);
        });
        
        document.addEventListener('audioEngine:playlistShuffled', () => {
            this.updateShuffleButton(true);
        });
        
        document.addEventListener('audioEngine:playlistUnshuffled', () => {
            this.updateShuffleButton(false);
        });
    }

    bindKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            // Don't handle shortcuts when typing in inputs
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
            
            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    this.togglePlayback();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.audioEngine.seek(this.audioEngine.getCurrentTime() - 10);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.audioEngine.seek(this.audioEngine.getCurrentTime() + 10);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.audioEngine.setVolume(Math.min(1, this.audioEngine.getVolume() + 0.1));
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.audioEngine.setVolume(Math.max(0, this.audioEngine.getVolume() - 0.1));
                    break;
                case 'KeyM':
                    e.preventDefault();
                    this.toggleMute();
                    break;
                case 'KeyS':
                    e.preventDefault();
                    this.toggleShuffle();
                    break;
                case 'KeyR':
                    e.preventDefault();
                    this.toggleRepeat();
                    break;
                case 'KeyN':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.audioEngine.next();
                    }
                    break;
                case 'KeyP':
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();
                        this.audioEngine.previous();
                    }
                    break;
            }
        });
    }

    // UI Update Methods
    updateTrackInfo(track) {
        if (!track) {
            this.trackTitle.textContent = 'Select a track to play';
            this.trackArtist.textContent = 'Unknown Artist';
            this.trackAlbum.textContent = 'Unknown Album';
            this.albumArt.src = 'assets/images/default-album.jpg';
            this.stickyTitle.textContent = '';
            this.stickyArtist.textContent = '';
            this.stickyAlbumArt.src = 'assets/images/default-album.jpg';
            return;
        }
        
        this.trackTitle.textContent = track.title || 'Unknown Title';
        this.trackArtist.textContent = track.artist || 'Unknown Artist';
        this.trackAlbum.textContent = track.album || 'Unknown Album';
        
        // Update album art
        if (track.artwork) {
            this.albumArt.src = track.artwork;
            this.stickyAlbumArt.src = track.artwork;
        } else {
            this.albumArt.src = 'assets/images/default-album.jpg';
            this.stickyAlbumArt.src = 'assets/images/default-album.jpg';
        }
        
        // Update sticky player
        this.stickyTitle.textContent = track.title || 'Unknown Title';
        this.stickyArtist.textContent = track.artist || 'Unknown Artist';
        
        // Update document title
        document.title = `${track.title} - ${track.artist} | MP3 Audio Player`;
    }

    updatePlayButton(isPlaying) {
        const playIcon = isPlaying ? 'fa-pause' : 'fa-play';
        
        // Main play buttons
        this.mainPlayBtn.querySelector('i').className = `fas ${playIcon}`;
        this.playPauseBtn.querySelector('i').className = `fas ${playIcon}`;
        this.stickyPlayBtn.querySelector('i').className = `fas ${playIcon}`;
        
        // Update button states
        this.mainPlayBtn.classList.toggle('playing', isPlaying);
        this.playPauseBtn.classList.toggle('playing', isPlaying);
        this.stickyPlayBtn.classList.toggle('playing', isPlaying);
    }

    updateProgress(data) {
        if (this.isDraggingProgress) return;
        
        const { currentTime, duration, progress } = data;
        const progressPercent = (progress || 0) * 100;
        
        // Update progress bars
        this.progressFill.style.width = `${progressPercent}%`;
        this.stickyProgressFill.style.width = `${progressPercent}%`;
        
        // Update progress handles
        if (this.progressHandle) {
            this.progressHandle.style.left = `${progressPercent}%`;
        }
        
        // Update time displays
        this.currentTime.textContent = this.formatTime(currentTime);
    }

    updateDuration(duration) {
        this.totalTime.textContent = this.formatTime(duration);
    }

    updateVolumeDisplay(volume, muted = false) {
        if (this.isDraggingVolume) return;
        
        const volumePercent = muted ? 0 : volume * 100;
        
        // Update volume bars
        this.volumeFill.style.width = `${volumePercent}%`;
        this.stickyVolumeFill.style.width = `${volumePercent}%`;
        
        // Update volume handles
        if (this.volumeHandle) {
            this.volumeHandle.style.right = `${100 - volumePercent}%`;
        }
        
        // Update mute button icons
        const volumeIcon = muted || volume === 0 ? 'fa-volume-mute' : 
                          volume < 0.5 ? 'fa-volume-down' : 'fa-volume-up';
        
        this.muteBtn.querySelector('i').className = `fas ${volumeIcon}`;
        this.stickyMuteBtn.querySelector('i').className = `fas ${volumeIcon}`;
    }

    updateShuffleButton(isShuffled) {
        this.shuffleBtn.classList.toggle('active', isShuffled);
        this.shuffleBtn.style.color = isShuffled ? 'var(--primary-color)' : '';
    }

    updateRepeatButton(mode) {
        this.repeatBtn.classList.remove('active', 'repeat-one');
        
        switch (mode) {
            case 'one':
                this.repeatBtn.classList.add('active', 'repeat-one');
                this.repeatBtn.querySelector('i').className = 'fas fa-redo';
                this.repeatBtn.style.color = 'var(--primary-color)';
                break;
            case 'all':
                this.repeatBtn.classList.add('active');
                this.repeatBtn.querySelector('i').className = 'fas fa-redo';
                this.repeatBtn.style.color = 'var(--primary-color)';
                break;
            default:
                this.repeatBtn.querySelector('i').className = 'fas fa-redo';
                this.repeatBtn.style.color = '';
                break;
        }
    }

    updateSpeedDisplay(rate) {
        this.speedBtn.querySelector('.speed-text').textContent = `${rate}x`;
    }

    updateStickyPlayerVisibility() {
        const shouldShow = this.audioEngine.getCurrentTrack() !== null;
        
        if (shouldShow !== this.stickyPlayerVisible) {
            this.stickyPlayerVisible = shouldShow;
            this.stickyPlayer.classList.toggle('active', shouldShow);
        }
    }

    // Control Methods
    togglePlayback() {
        if (this.audioEngine.isPlayingState()) {
            this.audioEngine.pause();
        } else {
            this.audioEngine.play();
        }
    }

    toggleShuffle() {
        this.audioEngine.toggleShuffle();
    }

    toggleRepeat() {
        this.audioEngine.toggleRepeat();
    }

    toggleMute() {
        this.audioEngine.toggleMute();
    }

    cyclePlaybackSpeed() {
        const speeds = [0.5, 0.75, 1, 1.25, 1.5, 2];
        const currentSpeed = this.audioEngine.getPlaybackRate();
        const currentIndex = speeds.indexOf(currentSpeed);
        const nextIndex = (currentIndex + 1) % speeds.length;
        
        this.audioEngine.setPlaybackRate(speeds[nextIndex]);
    }

    downloadCurrentTrack() {
        const track = this.audioEngine.getCurrentTrack();
        if (!track) {
            alert('No track selected');
            return;
        }
        
        this.playlistManager.downloadTrack(track.id);
    }

    shareCurrentTrack() {
        const track = this.audioEngine.getCurrentTrack();
        if (!track) {
            alert('No track selected');
            return;
        }
        
        if (navigator.share) {
            navigator.share({
                title: track.title,
                text: `Check out "${track.title}" by ${track.artist}`,
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            const shareText = `Check out "${track.title}" by ${track.artist} - ${window.location.href}`;
            navigator.clipboard.writeText(shareText).then(() => {
                alert('Share link copied to clipboard!');
            });
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        localStorage.setItem('mp3Player_theme', this.currentTheme);
    }

    applyTheme() {
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        
        // Dispatch theme change event for other components
        const event = new CustomEvent('themeChanged', {
            detail: { theme: this.currentTheme }
        });
        document.dispatchEvent(event);
    }

    handleScroll() {
        // Auto-hide/show sticky player based on main player visibility
        const playerSection = document.querySelector('.player-section');
        const rect = playerSection.getBoundingClientRect();
        const isMainPlayerVisible = rect.bottom > 0 && rect.top < window.innerHeight;
        
        // Only show sticky player when main player is not visible and there's a track
        const shouldShowSticky = !isMainPlayerVisible && this.audioEngine.getCurrentTrack();
        
        if (shouldShowSticky !== this.stickyPlayerVisible) {
            this.stickyPlayerVisible = shouldShowSticky;
            this.stickyPlayer.classList.toggle('active', shouldShowSticky);
        }
    }

    // Utility Methods
    formatTime(seconds) {
        if (!seconds || isNaN(seconds)) return '0:00';
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    saveState() {
        // Save current state to localStorage
        const state = {
            volume: this.audioEngine.getVolume(),
            playbackRate: this.audioEngine.getPlaybackRate(),
            repeatMode: this.audioEngine.getRepeatMode(),
            isShuffled: this.audioEngine.isShuffledState(),
            theme: this.currentTheme
        };
        
        localStorage.setItem('mp3Player_state', JSON.stringify(state));
    }

    loadState() {
        const savedState = localStorage.getItem('mp3Player_state');
        if (!savedState) return;
        
        try {
            const state = JSON.parse(savedState);
            
            if (state.volume !== undefined) {
                this.audioEngine.setVolume(state.volume);
            }
            
            if (state.playbackRate !== undefined) {
                this.audioEngine.setPlaybackRate(state.playbackRate);
            }
            
            if (state.repeatMode !== undefined) {
                this.audioEngine.setRepeatMode(state.repeatMode);
            }
            
            if (state.isShuffled && !this.audioEngine.isShuffledState()) {
                this.audioEngine.toggleShuffle();
            }
            
            if (state.theme) {
                this.currentTheme = state.theme;
                this.applyTheme();
            }
        } catch (error) {
            console.error('Failed to load saved state:', error);
        }
    }
}

// Export for use in other modules
window.UIController = UIController;
