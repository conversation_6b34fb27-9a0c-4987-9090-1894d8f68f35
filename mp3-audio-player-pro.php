<?php
/**
 * Plugin Name: MP3 Audio Player Pro
 * Plugin URI: https://github.com/your-username/mp3-audio-player-pro
 * Description: A professional, feature-rich MP3 audio player with waveform visualization, playlist management, and modern UI design. Inspired by Sonaar MP3 Audio Player.
 * Version: 1.0.0
 * Author: MP3 Audio Player Team
 * Author URI: https://github.com/your-username
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: mp3-audio-player-pro
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('MP3_AUDIO_PLAYER_PRO_VERSION', '1.0.0');
define('MP3_AUDIO_PLAYER_PRO_PLUGIN_URL', plugin_dir_url(__FILE__));
define('MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('MP3_AUDIO_PLAYER_PRO_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN', 'mp3-audio-player-pro');

/**
 * Main Plugin Class
 */
class MP3_Audio_Player_Pro {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Plugin activation/deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('MP3_Audio_Player_Pro', 'uninstall'));
        
        // WordPress hooks
        add_action('init', array($this, 'init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('wp_head', array($this, 'add_frontend_vars'));
        
        // Admin hooks
        if (is_admin()) {
            add_action('admin_menu', array($this, 'add_admin_menu'));
            add_action('admin_init', array($this, 'admin_init'));
        }
        
        // AJAX hooks
        add_action('wp_ajax_mp3_player_upload_file', array($this, 'handle_file_upload'));
        add_action('wp_ajax_mp3_player_save_playlist', array($this, 'handle_save_playlist'));
        add_action('wp_ajax_mp3_player_load_playlist', array($this, 'handle_load_playlist'));
        
        // Public AJAX (for non-logged-in users)
        add_action('wp_ajax_nopriv_mp3_player_load_playlist', array($this, 'handle_load_playlist'));
        
        // Shortcode
        add_shortcode('mp3_audio_player', array($this, 'shortcode_handler'));
        
        // Widget
        add_action('widgets_init', array($this, 'register_widget'));
        
        // REST API
        add_action('rest_api_init', array($this, 'register_rest_routes'));
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        // Include required files
        require_once MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH . 'includes/class-database.php';
        require_once MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH . 'includes/class-admin.php';
        require_once MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH . 'includes/class-shortcode.php';
        require_once MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH . 'includes/class-widget.php';
        require_once MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH . 'includes/class-rest-api.php';
        require_once MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH . 'includes/class-file-handler.php';
        require_once MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH . 'includes/functions.php';
        
        // Initialize classes
        MP3_Audio_Player_Pro_Database::get_instance();
        MP3_Audio_Player_Pro_Admin::get_instance();
        MP3_Audio_Player_Pro_Shortcode::get_instance();
        MP3_Audio_Player_Pro_REST_API::get_instance();
        MP3_Audio_Player_Pro_File_Handler::get_instance();
    }
    
    /**
     * Plugin initialization
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain(
            MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN,
            false,
            dirname(MP3_AUDIO_PLAYER_PRO_PLUGIN_BASENAME) . '/languages'
        );
        
        // Initialize database if needed
        $this->maybe_create_tables();
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Only enqueue on pages that use the player
        if ($this->should_enqueue_assets()) {
            // CSS
            wp_enqueue_style(
                'mp3-audio-player-pro-style',
                MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/css/style.css',
                array(),
                MP3_AUDIO_PLAYER_PRO_VERSION
            );
            
            // Google Fonts
            wp_enqueue_style(
                'mp3-audio-player-pro-fonts',
                'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
                array(),
                null
            );
            
            // Font Awesome
            wp_enqueue_style(
                'mp3-audio-player-pro-fontawesome',
                'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
                array(),
                '6.4.0'
            );
            
            // JavaScript
            wp_enqueue_script(
                'mp3-audio-player-pro-audio-engine',
                MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/audio-engine.js',
                array(),
                MP3_AUDIO_PLAYER_PRO_VERSION,
                true
            );
            
            wp_enqueue_script(
                'mp3-audio-player-pro-playlist-manager',
                MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/playlist-manager.js',
                array('mp3-audio-player-pro-audio-engine'),
                MP3_AUDIO_PLAYER_PRO_VERSION,
                true
            );
            
            wp_enqueue_script(
                'mp3-audio-player-pro-waveform',
                MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/waveform.js',
                array('mp3-audio-player-pro-audio-engine'),
                MP3_AUDIO_PLAYER_PRO_VERSION,
                true
            );
            
            wp_enqueue_script(
                'mp3-audio-player-pro-ui-controller',
                MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/ui-controller.js',
                array('mp3-audio-player-pro-audio-engine', 'mp3-audio-player-pro-playlist-manager'),
                MP3_AUDIO_PLAYER_PRO_VERSION,
                true
            );
            
            wp_enqueue_script(
                'mp3-audio-player-pro-file-manager',
                MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/file-manager.js',
                array('mp3-audio-player-pro-playlist-manager'),
                MP3_AUDIO_PLAYER_PRO_VERSION,
                true
            );
            
            wp_enqueue_script(
                'mp3-audio-player-pro-app',
                MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/app.js',
                array(
                    'mp3-audio-player-pro-audio-engine',
                    'mp3-audio-player-pro-playlist-manager',
                    'mp3-audio-player-pro-waveform',
                    'mp3-audio-player-pro-ui-controller',
                    'mp3-audio-player-pro-file-manager'
                ),
                MP3_AUDIO_PLAYER_PRO_VERSION,
                true
            );
        }
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only enqueue on plugin admin pages
        if (strpos($hook, 'mp3-audio-player') !== false) {
            wp_enqueue_style(
                'mp3-audio-player-pro-admin-style',
                MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/css/admin.css',
                array(),
                MP3_AUDIO_PLAYER_PRO_VERSION
            );
            
            wp_enqueue_script(
                'mp3-audio-player-pro-admin-script',
                MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/admin.js',
                array('jquery', 'wp-media'),
                MP3_AUDIO_PLAYER_PRO_VERSION,
                true
            );
            
            // Localize script for AJAX
            wp_localize_script(
                'mp3-audio-player-pro-admin-script',
                'mp3PlayerAdmin',
                array(
                    'ajaxUrl' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('mp3_player_admin_nonce'),
                    'strings' => array(
                        'confirmDelete' => __('Are you sure you want to delete this item?', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                        'uploadError' => __('Upload failed. Please try again.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                        'saveSuccess' => __('Settings saved successfully!', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                    )
                )
            );
        }
    }
    
    /**
     * Add frontend JavaScript variables
     */
    public function add_frontend_vars() {
        if ($this->should_enqueue_assets()) {
            ?>
            <script type="text/javascript">
                var mp3PlayerPro = {
                    ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
                    nonce: '<?php echo wp_create_nonce('mp3_player_frontend_nonce'); ?>',
                    pluginUrl: '<?php echo MP3_AUDIO_PLAYER_PRO_PLUGIN_URL; ?>',
                    settings: <?php echo json_encode($this->get_frontend_settings()); ?>
                };
            </script>
            <?php
        }
    }
    
    /**
     * Check if assets should be enqueued
     */
    private function should_enqueue_assets() {
        global $post;
        
        // Always enqueue on pages with shortcode
        if ($post && has_shortcode($post->post_content, 'mp3_audio_player')) {
            return true;
        }
        
        // Check if any widgets are active
        if (is_active_widget(false, false, 'mp3_audio_player_widget')) {
            return true;
        }
        
        // Check plugin settings for global loading
        $settings = get_option('mp3_audio_player_pro_settings', array());
        if (isset($settings['load_everywhere']) && $settings['load_everywhere']) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Get frontend settings
     */
    private function get_frontend_settings() {
        $settings = get_option('mp3_audio_player_pro_settings', array());
        
        return array(
            'theme' => isset($settings['default_theme']) ? $settings['default_theme'] : 'light',
            'autoplay' => isset($settings['autoplay']) ? $settings['autoplay'] : false,
            'volume' => isset($settings['default_volume']) ? $settings['default_volume'] : 1,
            'showWaveform' => isset($settings['show_waveform']) ? $settings['show_waveform'] : true,
            'stickyPlayer' => isset($settings['sticky_player']) ? $settings['sticky_player'] : true,
        );
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('MP3 Audio Player Pro', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            __('MP3 Player', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            'manage_options',
            'mp3-audio-player-pro',
            array($this, 'admin_page'),
            'dashicons-format-audio',
            30
        );
        
        add_submenu_page(
            'mp3-audio-player-pro',
            __('Playlists', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            __('Playlists', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            'manage_options',
            'mp3-audio-player-playlists',
            array($this, 'playlists_page')
        );
        
        add_submenu_page(
            'mp3-audio-player-pro',
            __('Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            __('Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            'manage_options',
            'mp3-audio-player-settings',
            array($this, 'settings_page')
        );
    }
    
    /**
     * Admin page callback
     */
    public function admin_page() {
        include MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH . 'admin/views/main.php';
    }
    
    /**
     * Playlists page callback
     */
    public function playlists_page() {
        include MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH . 'admin/views/playlists.php';
    }
    
    /**
     * Settings page callback
     */
    public function settings_page() {
        include MP3_AUDIO_PLAYER_PRO_PLUGIN_PATH . 'admin/views/settings.php';
    }
    
    /**
     * Admin initialization
     */
    public function admin_init() {
        // Register settings
        register_setting('mp3_audio_player_pro_settings', 'mp3_audio_player_pro_settings');
    }
    
    /**
     * Shortcode handler
     */
    public function shortcode_handler($atts) {
        return MP3_Audio_Player_Pro_Shortcode::get_instance()->render($atts);
    }
    
    /**
     * Register widget
     */
    public function register_widget() {
        register_widget('MP3_Audio_Player_Pro_Widget');
    }
    
    /**
     * Register REST API routes
     */
    public function register_rest_routes() {
        MP3_Audio_Player_Pro_REST_API::get_instance()->register_routes();
    }
    
    /**
     * Handle file upload AJAX
     */
    public function handle_file_upload() {
        MP3_Audio_Player_Pro_File_Handler::get_instance()->handle_upload();
    }
    
    /**
     * Handle save playlist AJAX
     */
    public function handle_save_playlist() {
        // Implementation in includes/class-file-handler.php
    }
    
    /**
     * Handle load playlist AJAX
     */
    public function handle_load_playlist() {
        // Implementation in includes/class-file-handler.php
    }
    
    /**
     * Create database tables if needed
     */
    private function maybe_create_tables() {
        $db_version = get_option('mp3_audio_player_pro_db_version', '0');
        
        if (version_compare($db_version, MP3_AUDIO_PLAYER_PRO_VERSION, '<')) {
            MP3_Audio_Player_Pro_Database::get_instance()->create_tables();
            update_option('mp3_audio_player_pro_db_version', MP3_AUDIO_PLAYER_PRO_VERSION);
        }
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        MP3_Audio_Player_Pro_Database::get_instance()->create_tables();
        
        // Set default options
        $default_settings = array(
            'default_theme' => 'light',
            'autoplay' => false,
            'default_volume' => 1,
            'show_waveform' => true,
            'sticky_player' => true,
            'load_everywhere' => false,
        );
        
        add_option('mp3_audio_player_pro_settings', $default_settings);
        add_option('mp3_audio_player_pro_db_version', MP3_AUDIO_PLAYER_PRO_VERSION);
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin uninstall
     */
    public static function uninstall() {
        // Remove database tables
        MP3_Audio_Player_Pro_Database::get_instance()->drop_tables();
        
        // Remove options
        delete_option('mp3_audio_player_pro_settings');
        delete_option('mp3_audio_player_pro_db_version');
        
        // Remove uploaded files (optional)
        $upload_dir = wp_upload_dir();
        $plugin_upload_dir = $upload_dir['basedir'] . '/mp3-audio-player-pro';
        
        if (is_dir($plugin_upload_dir)) {
            // Recursively delete directory
            self::delete_directory($plugin_upload_dir);
        }
    }
    
    /**
     * Recursively delete directory
     */
    private static function delete_directory($dir) {
        if (!is_dir($dir)) {
            return false;
        }
        
        $files = array_diff(scandir($dir), array('.', '..'));
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? self::delete_directory($path) : unlink($path);
        }
        
        return rmdir($dir);
    }
}

// Initialize the plugin
MP3_Audio_Player_Pro::get_instance();
