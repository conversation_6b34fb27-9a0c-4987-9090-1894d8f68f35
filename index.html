<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MP3 Audio Player - Professional Music Player</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Main Container -->
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="logo">
                    <i class="fas fa-music"></i>
                    MP3 Audio Player
                </h1>
                <div class="header-controls">
                    <button class="btn btn-secondary" id="uploadBtn">
                        <i class="fas fa-upload"></i>
                        Upload Music
                    </button>
                    <button class="btn btn-secondary" id="themeToggle">
                        <i class="fas fa-palette"></i>
                        Theme
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Player Section -->
            <section class="player-section">
                <div class="player-container">
                    <!-- Album Art -->
                    <div class="album-art-container">
                        <div class="album-art">
                            <img id="albumArt" src="assets/images/default-album.jpg" alt="Album Art">
                            <div class="album-art-overlay">
                                <button class="play-btn-large" id="mainPlayBtn">
                                    <i class="fas fa-play"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Track Info -->
                    <div class="track-info">
                        <h2 class="track-title" id="trackTitle">Select a track to play</h2>
                        <p class="track-artist" id="trackArtist">Unknown Artist</p>
                        <p class="track-album" id="trackAlbum">Unknown Album</p>
                    </div>

                    <!-- Waveform Container -->
                    <div class="waveform-container">
                        <canvas id="waveformCanvas" class="waveform-canvas"></canvas>
                        <div class="waveform-progress" id="waveformProgress"></div>
                    </div>

                    <!-- Player Controls -->
                    <div class="player-controls">
                        <div class="control-buttons">
                            <button class="control-btn" id="shuffleBtn">
                                <i class="fas fa-random"></i>
                            </button>
                            <button class="control-btn" id="prevBtn">
                                <i class="fas fa-step-backward"></i>
                            </button>
                            <button class="control-btn play-pause-btn" id="playPauseBtn">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="control-btn" id="nextBtn">
                                <i class="fas fa-step-forward"></i>
                            </button>
                            <button class="control-btn" id="repeatBtn">
                                <i class="fas fa-redo"></i>
                            </button>
                        </div>

                        <!-- Progress Bar -->
                        <div class="progress-container">
                            <span class="time-display" id="currentTime">0:00</span>
                            <div class="progress-bar" id="progressBar">
                                <div class="progress-fill" id="progressFill"></div>
                                <div class="progress-handle" id="progressHandle"></div>
                            </div>
                            <span class="time-display" id="totalTime">0:00</span>
                        </div>

                        <!-- Volume Control -->
                        <div class="volume-container">
                            <button class="control-btn" id="muteBtn">
                                <i class="fas fa-volume-up"></i>
                            </button>
                            <div class="volume-bar" id="volumeBar">
                                <div class="volume-fill" id="volumeFill"></div>
                                <div class="volume-handle" id="volumeHandle"></div>
                            </div>
                        </div>

                        <!-- Additional Controls -->
                        <div class="additional-controls">
                            <button class="control-btn" id="speedBtn" title="Playback Speed">
                                <span class="speed-text">1x</span>
                            </button>
                            <button class="control-btn" id="downloadBtn" title="Download">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="control-btn" id="shareBtn" title="Share">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Playlist Section -->
            <section class="playlist-section">
                <div class="playlist-header">
                    <h3>Playlist</h3>
                    <div class="playlist-controls">
                        <button class="btn btn-small" id="clearPlaylistBtn">
                            <i class="fas fa-trash"></i>
                            Clear
                        </button>
                        <button class="btn btn-small" id="savePlaylistBtn">
                            <i class="fas fa-save"></i>
                            Save
                        </button>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="search-filter-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search tracks...">
                    </div>
                    <div class="filter-controls">
                        <select id="sortBy">
                            <option value="title">Sort by Title</option>
                            <option value="artist">Sort by Artist</option>
                            <option value="album">Sort by Album</option>
                            <option value="duration">Sort by Duration</option>
                        </select>
                    </div>
                </div>

                <!-- Playlist Container -->
                <div class="playlist-container" id="playlistContainer">
                    <div class="playlist-empty">
                        <i class="fas fa-music"></i>
                        <p>No tracks in playlist</p>
                        <p class="subtitle">Upload some music to get started</p>
                    </div>
                </div>
            </section>
        </main>

        <!-- File Upload Modal -->
        <div class="modal" id="uploadModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Upload Music Files</h3>
                    <button class="modal-close" id="modalClose">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="upload-area" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>Drag & drop MP3 files here or click to browse</p>
                        <input type="file" id="fileInput" multiple accept="audio/mp3,audio/mpeg">
                        <button class="btn btn-primary" id="browseBtn">Browse Files</button>
                    </div>
                    <div class="upload-progress" id="uploadProgress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="uploadProgressFill"></div>
                        </div>
                        <p id="uploadStatus">Uploading...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sticky Footer Player -->
    <div class="sticky-player" id="stickyPlayer" style="display: none;">
        <div class="sticky-player-content">
            <div class="sticky-track-info">
                <img class="sticky-album-art" id="stickyAlbumArt" src="assets/images/default-album.jpg" alt="Album Art">
                <div class="sticky-text-info">
                    <span class="sticky-title" id="stickyTitle">Track Title</span>
                    <span class="sticky-artist" id="stickyArtist">Artist</span>
                </div>
            </div>
            
            <div class="sticky-controls">
                <button class="sticky-btn" id="stickyPrevBtn">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button class="sticky-btn sticky-play-btn" id="stickyPlayBtn">
                    <i class="fas fa-play"></i>
                </button>
                <button class="sticky-btn" id="stickyNextBtn">
                    <i class="fas fa-step-forward"></i>
                </button>
            </div>

            <div class="sticky-progress">
                <div class="sticky-progress-bar" id="stickyProgressBar">
                    <div class="sticky-progress-fill" id="stickyProgressFill"></div>
                </div>
            </div>

            <div class="sticky-volume">
                <button class="sticky-btn" id="stickyMuteBtn">
                    <i class="fas fa-volume-up"></i>
                </button>
                <div class="sticky-volume-bar" id="stickyVolumeBar">
                    <div class="sticky-volume-fill" id="stickyVolumeFill"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Audio Element -->
    <audio id="audioPlayer" preload="metadata"></audio>

    <!-- Scripts -->
    <script src="js/audio-engine.js"></script>
    <script src="js/playlist-manager.js"></script>
    <script src="js/waveform.js"></script>
    <script src="js/ui-controller.js"></script>
    <script src="js/file-manager.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
