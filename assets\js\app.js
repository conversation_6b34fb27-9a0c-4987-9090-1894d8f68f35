/**
 * Main Application - MP3 Audio Player
 * Initializes and coordinates all components
 */

class MP3AudioPlayer {
    constructor() {
        this.audioEngine = null;
        this.playlistManager = null;
        this.waveformVisualizer = null;
        this.uiController = null;
        this.fileManager = null;
        
        this.isInitialized = false;
        this.version = '1.0.0';
        
        this.init();
    }

    async init() {
        try {
            console.log('🎵 Initializing MP3 Audio Player v' + this.version);
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }
            
            // Initialize core components
            this.initializeComponents();
            
            // Load saved state
            this.loadSavedState();
            
            // Add demo tracks if no tracks exist
            this.addDemoTracks();
            
            // Setup global error handling
            this.setupErrorHandling();
            
            // Setup performance monitoring
            this.setupPerformanceMonitoring();
            
            this.isInitialized = true;
            console.log('✅ MP3 Audio Player initialized successfully');
            
            // Dispatch ready event
            this.dispatchEvent('ready');
            
        } catch (error) {
            console.error('❌ Failed to initialize MP3 Audio Player:', error);
            this.handleInitializationError(error);
        }
    }

    initializeComponents() {
        // Initialize in dependency order
        this.audioEngine = new AudioEngine();
        this.playlistManager = new PlaylistManager(this.audioEngine);
        this.waveformVisualizer = new WaveformVisualizer(this.audioEngine);
        this.uiController = new UIController(this.audioEngine, this.playlistManager);
        this.fileManager = new FileManager(this.playlistManager);
        
        // Setup component communication
        this.setupComponentCommunication();
        
        console.log('🔧 All components initialized');
    }

    setupComponentCommunication() {
        // Cross-component event handling
        
        // When playlist changes, update waveform
        document.addEventListener('playlistManager:trackAdded', () => {
            this.updateStats();
        });
        
        document.addEventListener('playlistManager:trackRemoved', () => {
            this.updateStats();
        });
        
        document.addEventListener('playlistManager:playlistCleared', () => {
            this.updateStats();
        });
        
        // When audio engine state changes, update UI
        document.addEventListener('audioEngine:trackLoaded', (e) => {
            this.onTrackLoaded(e.detail.track);
        });
        
        document.addEventListener('audioEngine:error', (e) => {
            this.handleAudioError(e.detail.error);
        });
        
        // Performance monitoring
        document.addEventListener('audioEngine:play', () => {
            this.trackEvent('audio_play');
        });
        
        document.addEventListener('audioEngine:pause', () => {
            this.trackEvent('audio_pause');
        });
    }

    loadSavedState() {
        try {
            // Load UI state
            this.uiController.loadState();
            
            // Load saved playlists
            this.loadSavedPlaylists();
            
            console.log('💾 Saved state loaded');
        } catch (error) {
            console.warn('⚠️ Failed to load saved state:', error);
        }
    }

    loadSavedPlaylists() {
        const savedPlaylists = localStorage.getItem('mp3Player_playlists');
        if (!savedPlaylists) return;
        
        try {
            const playlists = JSON.parse(savedPlaylists);
            
            // For now, just load the most recent playlist
            if (playlists.length > 0) {
                const mostRecent = playlists[playlists.length - 1];
                // Note: We can't load file-based tracks from localStorage
                // This would need a more sophisticated storage solution
                console.log(`📋 Found ${playlists.length} saved playlist(s)`);
            }
        } catch (error) {
            console.warn('⚠️ Failed to load saved playlists:', error);
        }
    }

    addDemoTracks() {
        // Add some demo tracks if no tracks exist
        if (this.playlistManager.getTrackCount() === 0) {
            const demoTracks = [
                {
                    id: 'demo_1',
                    title: 'Demo Track 1',
                    artist: 'Demo Artist',
                    album: 'Demo Album',
                    duration: 180,
                    src: 'assets/audio/demo1.mp3', // These would need to exist
                    artwork: 'assets/images/demo-album1.jpg'
                },
                {
                    id: 'demo_2',
                    title: 'Demo Track 2',
                    artist: 'Demo Artist',
                    album: 'Demo Album',
                    duration: 210,
                    src: 'assets/audio/demo2.mp3',
                    artwork: 'assets/images/demo-album2.jpg'
                }
            ];
            
            // Only add demo tracks if the files exist
            // In a real implementation, you'd check if these files exist
            console.log('📝 Demo tracks available (upload your own music to get started)');
        }
    }

    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
            this.handleGlobalError(e.error);
        });
        
        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            this.handleGlobalError(e.reason);
        });
        
        // Audio-specific error handling
        document.addEventListener('audioEngine:error', (e) => {
            this.handleAudioError(e.detail.error);
        });
    }

    setupPerformanceMonitoring() {
        // Monitor performance metrics
        if ('performance' in window) {
            // Track initialization time
            const initTime = performance.now();
            console.log(`⚡ Initialization completed in ${initTime.toFixed(2)}ms`);
            
            // Monitor memory usage (if available)
            if ('memory' in performance) {
                setInterval(() => {
                    const memory = performance.memory;
                    if (memory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB
                        console.warn('⚠️ High memory usage detected:', {
                            used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                            total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB'
                        });
                    }
                }, 30000); // Check every 30 seconds
            }
        }
    }

    // Event handlers
    onTrackLoaded(track) {
        console.log('🎵 Track loaded:', track.title);
        this.updateDocumentTitle(track);
        this.updateMediaSession(track);
    }

    handleAudioError(error) {
        console.error('🔊 Audio error:', error);
        
        // Show user-friendly error message
        const errorMessages = {
            'MEDIA_ERR_ABORTED': 'Playback was aborted',
            'MEDIA_ERR_NETWORK': 'Network error occurred',
            'MEDIA_ERR_DECODE': 'Audio file could not be decoded',
            'MEDIA_ERR_SRC_NOT_SUPPORTED': 'Audio format not supported'
        };
        
        const message = errorMessages[error.code] || 'An audio error occurred';
        this.showNotification(message, 'error');
    }

    handleGlobalError(error) {
        console.error('🚨 Global error:', error);
        
        // Don't overwhelm user with technical errors
        if (this.isInitialized) {
            this.showNotification('An unexpected error occurred', 'error');
        }
    }

    handleInitializationError(error) {
        // Show fallback UI for initialization errors
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; flex-direction: column; font-family: Inter, sans-serif;">
                <h1 style="color: #ef4444; margin-bottom: 1rem;">⚠️ Initialization Failed</h1>
                <p style="color: #64748b; margin-bottom: 2rem;">The audio player could not be initialized.</p>
                <button onclick="location.reload()" style="padding: 0.5rem 1rem; background: #6366f1; color: white; border: none; border-radius: 0.5rem; cursor: pointer;">
                    Reload Page
                </button>
            </div>
        `;
    }

    // Utility methods
    updateDocumentTitle(track) {
        if (track) {
            document.title = `${track.title} - ${track.artist} | MP3 Audio Player`;
        } else {
            document.title = 'MP3 Audio Player';
        }
    }

    updateMediaSession(track) {
        // Update Media Session API for better browser integration
        if ('mediaSession' in navigator && track) {
            navigator.mediaSession.metadata = new MediaMetadata({
                title: track.title,
                artist: track.artist,
                album: track.album,
                artwork: track.artwork ? [
                    { src: track.artwork, sizes: '300x300', type: 'image/jpeg' }
                ] : []
            });
            
            // Set action handlers
            navigator.mediaSession.setActionHandler('play', () => {
                this.audioEngine.play();
            });
            
            navigator.mediaSession.setActionHandler('pause', () => {
                this.audioEngine.pause();
            });
            
            navigator.mediaSession.setActionHandler('previoustrack', () => {
                this.audioEngine.previous();
            });
            
            navigator.mediaSession.setActionHandler('nexttrack', () => {
                this.audioEngine.next();
            });
            
            navigator.mediaSession.setActionHandler('seekto', (details) => {
                this.audioEngine.seek(details.seekTime);
            });
        }
    }

    updateStats() {
        const trackCount = this.playlistManager.getTrackCount();
        console.log(`📊 Playlist stats: ${trackCount} track(s)`);
    }

    showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
            background: ${type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#6366f1'};
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    trackEvent(eventName, data = {}) {
        // Analytics/tracking (placeholder)
        console.log(`📈 Event: ${eventName}`, data);
        
        // In a real app, you'd send this to your analytics service
        // Example: gtag('event', eventName, data);
    }

    // Public API methods
    getVersion() {
        return this.version;
    }

    getAudioEngine() {
        return this.audioEngine;
    }

    getPlaylistManager() {
        return this.playlistManager;
    }

    getUIController() {
        return this.uiController;
    }

    isReady() {
        return this.isInitialized;
    }

    // Cleanup
    destroy() {
        console.log('🧹 Cleaning up MP3 Audio Player');
        
        if (this.audioEngine) {
            this.audioEngine.destroy();
        }
        
        if (this.fileManager) {
            this.fileManager.cleanup();
        }
        
        if (this.waveformVisualizer) {
            this.waveformVisualizer.destroy();
        }
        
        // Save state before cleanup
        if (this.uiController) {
            this.uiController.saveState();
        }
        
        this.isInitialized = false;
    }

    // Event system
    dispatchEvent(eventName, data = {}) {
        const event = new CustomEvent(`mp3Player:${eventName}`, {
            detail: data
        });
        document.dispatchEvent(event);
    }
}

// CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize the application
const mp3Player = new MP3AudioPlayer();

// Make it globally available for debugging
window.mp3Player = mp3Player;

// Handle page unload
window.addEventListener('beforeunload', () => {
    mp3Player.destroy();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MP3AudioPlayer;
}
