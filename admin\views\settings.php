<?php
/**
 * Settings Admin Page
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle form submission
if (isset($_POST['submit']) && wp_verify_nonce($_POST['_wpnonce'], 'mp3_audio_player_pro_settings-options')) {
    update_option('mp3_audio_player_pro_settings', $_POST['mp3_audio_player_pro_settings']);
    echo '<div class="notice notice-success"><p>' . __('Settings saved successfully!', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN) . '</p></div>';
}

$settings = get_option('mp3_audio_player_pro_settings', array());
?>

<div class="wrap">
    <h1><?php _e('MP3 Audio Player Pro Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h1>
    
    <form method="post" action="">
        <?php wp_nonce_field('mp3_audio_player_pro_settings-options'); ?>
        
        <div class="mp3-player-settings-container">
            <!-- General Settings -->
            <div class="settings-section">
                <h2><?php _e('General Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Default Theme', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <select name="mp3_audio_player_pro_settings[default_theme]">
                                <option value="light" <?php selected(isset($settings['default_theme']) ? $settings['default_theme'] : 'light', 'light'); ?>>
                                    <?php _e('Light', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                                </option>
                                <option value="dark" <?php selected(isset($settings['default_theme']) ? $settings['default_theme'] : 'light', 'dark'); ?>>
                                    <?php _e('Dark', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                                </option>
                            </select>
                            <p class="description"><?php _e('Default theme for new players.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Autoplay', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="mp3_audio_player_pro_settings[autoplay]" value="1" 
                                       <?php checked(isset($settings['autoplay']) ? $settings['autoplay'] : false, true); ?>>
                                <?php _e('Enable autoplay by default', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </label>
                            <p class="description"><?php _e('Note: Most browsers block autoplay with sound.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Default Volume', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <?php $volume = isset($settings['default_volume']) ? $settings['default_volume'] : 1; ?>
                            <input type="range" name="mp3_audio_player_pro_settings[default_volume]" 
                                   min="0" max="1" step="0.1" value="<?php echo esc_attr($volume); ?>" 
                                   oninput="this.nextElementSibling.value = Math.round(this.value * 100) + '%'">
                            <output><?php echo round($volume * 100); ?>%</output>
                            <p class="description"><?php _e('Default volume level (0-100%).', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Appearance Settings -->
            <div class="settings-section">
                <h2><?php _e('Appearance Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Show Waveform', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="mp3_audio_player_pro_settings[show_waveform]" value="1" 
                                       <?php checked(isset($settings['show_waveform']) ? $settings['show_waveform'] : true, true); ?>>
                                <?php _e('Show waveform visualization by default', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Sticky Player', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="mp3_audio_player_pro_settings[sticky_player]" value="1" 
                                       <?php checked(isset($settings['sticky_player']) ? $settings['sticky_player'] : true, true); ?>>
                                <?php _e('Enable sticky player by default', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Custom CSS', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <textarea name="mp3_audio_player_pro_settings[custom_css]" rows="10" cols="50" class="large-text code"><?php echo esc_textarea(isset($settings['custom_css']) ? $settings['custom_css'] : ''); ?></textarea>
                            <p class="description"><?php _e('Add custom CSS to style your players.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Advanced Settings -->
            <div class="settings-section">
                <h2><?php _e('Advanced Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Load Assets Everywhere', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="mp3_audio_player_pro_settings[load_everywhere]" value="1" 
                                       <?php checked(isset($settings['load_everywhere']) ? $settings['load_everywhere'] : false, true); ?>>
                                <?php _e('Load player assets on all pages', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </label>
                            <p class="description"><?php _e('Enable if you have players in widgets or theme files.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Enable Analytics', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="mp3_audio_player_pro_settings[enable_analytics]" value="1" 
                                       <?php checked(isset($settings['enable_analytics']) ? $settings['enable_analytics'] : false, true); ?>>
                                <?php _e('Enable play/pause analytics tracking', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </label>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Maximum File Size', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <?php $max_size = isset($settings['max_file_size']) ? $settings['max_file_size'] : 50; ?>
                            <input type="number" name="mp3_audio_player_pro_settings[max_file_size]" 
                                   value="<?php echo esc_attr($max_size); ?>" min="1" max="500" step="1">
                            <span><?php _e('MB', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></span>
                            <p class="description"><?php _e('Maximum file size for audio uploads.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- File Format Settings -->
            <div class="settings-section">
                <h2><?php _e('Supported File Formats', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Allowed Formats', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <?php 
                            $formats = mp3_audio_player_pro_get_supported_formats();
                            $allowed_formats = isset($settings['allowed_formats']) ? $settings['allowed_formats'] : array_keys($formats);
                            ?>
                            
                            <?php foreach ($formats as $ext => $format): ?>
                                <label style="display: block; margin-bottom: 5px;">
                                    <input type="checkbox" name="mp3_audio_player_pro_settings[allowed_formats][]" 
                                           value="<?php echo esc_attr($ext); ?>"
                                           <?php checked(in_array($ext, $allowed_formats), true); ?>>
                                    <?php echo esc_html($format['name']); ?> (.<?php echo esc_html($ext); ?>)
                                </label>
                            <?php endforeach; ?>
                            
                            <p class="description"><?php _e('Select which audio formats are allowed for upload.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Import/Export Settings -->
            <div class="settings-section">
                <h2><?php _e('Import/Export', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Export Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <button type="button" class="button" onclick="exportSettings()">
                                <?php _e('Export Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </button>
                            <p class="description"><?php _e('Download your current settings as a JSON file.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Import Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <input type="file" id="import-settings" accept=".json">
                            <button type="button" class="button" onclick="importSettings()">
                                <?php _e('Import Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </button>
                            <p class="description"><?php _e('Upload a settings JSON file to restore configuration.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- System Information -->
            <div class="settings-section">
                <h2><?php _e('System Information', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Plugin Version', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td><?php echo MP3_AUDIO_PLAYER_PRO_VERSION; ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('WordPress Version', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td><?php echo get_bloginfo('version'); ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('PHP Version', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td><?php echo PHP_VERSION; ?></td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Upload Directory', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <?php 
                            $upload_dir = mp3_audio_player_pro_get_upload_dir();
                            echo esc_html($upload_dir['path']);
                            echo is_writable($upload_dir['path']) ? ' <span style="color: green;">✓ Writable</span>' : ' <span style="color: red;">✗ Not Writable</span>';
                            ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <?php submit_button(); ?>
    </form>
</div>

<style>
.mp3-player-settings-container {
    max-width: 1000px;
}

.settings-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.settings-section h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.form-table th {
    width: 200px;
}

input[type="range"] {
    width: 200px;
}

output {
    margin-left: 10px;
    font-weight: bold;
}
</style>

<script>
function exportSettings() {
    var settings = <?php echo json_encode($settings); ?>;
    var dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(settings, null, 2));
    var downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", "mp3-audio-player-pro-settings.json");
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
}

function importSettings() {
    var fileInput = document.getElementById('import-settings');
    var file = fileInput.files[0];
    
    if (!file) {
        alert('<?php _e('Please select a file to import.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>');
        return;
    }
    
    var reader = new FileReader();
    reader.onload = function(e) {
        try {
            var settings = JSON.parse(e.target.result);
            
            // Populate form fields
            for (var key in settings) {
                var input = document.querySelector('[name="mp3_audio_player_pro_settings[' + key + ']"]');
                if (input) {
                    if (input.type === 'checkbox') {
                        input.checked = !!settings[key];
                    } else {
                        input.value = settings[key];
                    }
                }
            }
            
            alert('<?php _e('Settings imported successfully! Please save to apply changes.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>');
        } catch (error) {
            alert('<?php _e('Invalid settings file.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>');
        }
    };
    
    reader.readAsText(file);
}
</script>
