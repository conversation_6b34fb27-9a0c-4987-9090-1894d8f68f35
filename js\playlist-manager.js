/**
 * Playlist Manager - Handles playlist operations and UI
 * Manages track collection, search, filtering, and playlist display
 */

class PlaylistManager {
    constructor(audioEngine) {
        this.audioEngine = audioEngine;
        this.tracks = [];
        this.filteredTracks = [];
        this.currentFilter = '';
        this.currentSort = 'title';
        this.sortDirection = 'asc';
        
        // DOM elements
        this.playlistContainer = document.getElementById('playlistContainer');
        this.searchInput = document.getElementById('searchInput');
        this.sortSelect = document.getElementById('sortBy');
        this.clearBtn = document.getElementById('clearPlaylistBtn');
        this.saveBtn = document.getElementById('savePlaylistBtn');
        
        this.bindEvents();
        this.updateDisplay();
    }

    bindEvents() {
        // Search functionality
        this.searchInput.addEventListener('input', (e) => {
            this.currentFilter = e.target.value.toLowerCase();
            this.filterTracks();
        });

        // Sort functionality
        this.sortSelect.addEventListener('change', (e) => {
            this.currentSort = e.target.value;
            this.sortTracks();
        });

        // Playlist controls
        this.clearBtn.addEventListener('click', () => this.clearPlaylist());
        this.saveBtn.addEventListener('click', () => this.savePlaylist());

        // Audio engine events
        document.addEventListener('audioEngine:trackLoaded', (e) => {
            this.updateActiveTrack(e.detail.track);
        });

        document.addEventListener('audioEngine:play', () => {
            this.updatePlayingState(true);
        });

        document.addEventListener('audioEngine:pause', () => {
            this.updatePlayingState(false);
        });
    }

    // Track management
    addTrack(track) {
        // Generate unique ID if not provided
        if (!track.id) {
            track.id = this.generateTrackId();
        }

        // Ensure required properties
        track.title = track.title || 'Unknown Title';
        track.artist = track.artist || 'Unknown Artist';
        track.album = track.album || 'Unknown Album';
        track.duration = track.duration || 0;
        track.src = track.src || '';

        this.tracks.push(track);
        this.filterTracks();
        this.updateAudioEnginePlaylist();
        
        this.dispatchEvent('trackAdded', { track });
    }

    addTracks(tracks) {
        tracks.forEach(track => this.addTrack(track));
    }

    removeTrack(trackId) {
        const index = this.tracks.findIndex(track => track.id === trackId);
        if (index !== -1) {
            const removedTrack = this.tracks.splice(index, 1)[0];
            this.filterTracks();
            this.updateAudioEnginePlaylist();
            
            this.dispatchEvent('trackRemoved', { track: removedTrack });
        }
    }

    removeTrackAtIndex(index) {
        if (index >= 0 && index < this.tracks.length) {
            const removedTrack = this.tracks.splice(index, 1)[0];
            this.filterTracks();
            this.updateAudioEnginePlaylist();
            
            this.dispatchEvent('trackRemoved', { track: removedTrack });
        }
    }

    clearPlaylist() {
        if (this.tracks.length === 0) return;
        
        if (confirm('Are you sure you want to clear the entire playlist?')) {
            this.tracks = [];
            this.filteredTracks = [];
            this.updateDisplay();
            this.updateAudioEnginePlaylist();
            
            this.dispatchEvent('playlistCleared');
        }
    }

    // Search and filter
    filterTracks() {
        if (!this.currentFilter) {
            this.filteredTracks = [...this.tracks];
        } else {
            this.filteredTracks = this.tracks.filter(track => {
                const searchText = this.currentFilter;
                return (
                    track.title.toLowerCase().includes(searchText) ||
                    track.artist.toLowerCase().includes(searchText) ||
                    track.album.toLowerCase().includes(searchText)
                );
            });
        }
        
        this.sortTracks();
    }

    // Sorting
    sortTracks() {
        this.filteredTracks.sort((a, b) => {
            let aValue = a[this.currentSort];
            let bValue = b[this.currentSort];
            
            // Handle different data types
            if (this.currentSort === 'duration') {
                aValue = parseFloat(aValue) || 0;
                bValue = parseFloat(bValue) || 0;
            } else {
                aValue = String(aValue).toLowerCase();
                bValue = String(bValue).toLowerCase();
            }
            
            let comparison = 0;
            if (aValue > bValue) {
                comparison = 1;
            } else if (aValue < bValue) {
                comparison = -1;
            }
            
            return this.sortDirection === 'asc' ? comparison : -comparison;
        });
        
        this.updateDisplay();
    }

    toggleSortDirection() {
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        this.sortTracks();
    }

    // Display management
    updateDisplay() {
        if (this.filteredTracks.length === 0) {
            this.showEmptyState();
        } else {
            this.renderPlaylist();
        }
    }

    showEmptyState() {
        this.playlistContainer.innerHTML = `
            <div class="playlist-empty">
                <i class="fas fa-music"></i>
                <p>No tracks found</p>
                <p class="subtitle">${this.currentFilter ? 'Try a different search term' : 'Upload some music to get started'}</p>
            </div>
        `;
    }

    renderPlaylist() {
        const playlistHTML = this.filteredTracks.map((track, index) => {
            const originalIndex = this.tracks.findIndex(t => t.id === track.id);
            return this.createTrackElement(track, index, originalIndex);
        }).join('');
        
        this.playlistContainer.innerHTML = playlistHTML;
        this.bindTrackEvents();
    }

    createTrackElement(track, displayIndex, originalIndex) {
        const duration = this.formatDuration(track.duration);
        const isActive = this.audioEngine.getCurrentTrack()?.id === track.id;
        const isPlaying = isActive && this.audioEngine.isPlayingState();
        
        return `
            <div class="playlist-item ${isActive ? 'active' : ''} ${isPlaying ? 'playing' : ''}" 
                 data-track-id="${track.id}" 
                 data-original-index="${originalIndex}">
                <div class="playlist-item-number">${displayIndex + 1}</div>
                <button class="playlist-item-play" data-action="play">
                    <i class="fas ${isPlaying ? 'fa-pause' : 'fa-play'}"></i>
                </button>
                <div class="playlist-item-info">
                    <div class="playlist-item-title">${this.escapeHtml(track.title)}</div>
                    <div class="playlist-item-meta">
                        <span>${this.escapeHtml(track.artist)}</span>
                        <span>•</span>
                        <span>${this.escapeHtml(track.album)}</span>
                    </div>
                </div>
                <div class="playlist-item-duration">${duration}</div>
                <div class="playlist-item-actions">
                    <button class="playlist-item-action" data-action="download" title="Download">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="playlist-item-action" data-action="remove" title="Remove">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    bindTrackEvents() {
        const trackItems = this.playlistContainer.querySelectorAll('.playlist-item');
        
        trackItems.forEach(item => {
            const trackId = item.dataset.trackId;
            const originalIndex = parseInt(item.dataset.originalIndex);
            
            // Double click to play
            item.addEventListener('dblclick', () => {
                this.playTrack(trackId);
            });
            
            // Button actions
            const playBtn = item.querySelector('[data-action="play"]');
            const downloadBtn = item.querySelector('[data-action="download"]');
            const removeBtn = item.querySelector('[data-action="remove"]');
            
            if (playBtn) {
                playBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleTrackPlayback(trackId);
                });
            }
            
            if (downloadBtn) {
                downloadBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.downloadTrack(trackId);
                });
            }
            
            if (removeBtn) {
                removeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.removeTrack(trackId);
                });
            }
        });
    }

    // Playback control
    playTrack(trackId) {
        const track = this.tracks.find(t => t.id === trackId);
        if (!track) return;
        
        const trackIndex = this.tracks.findIndex(t => t.id === trackId);
        this.audioEngine.setPlaylist(this.tracks, trackIndex);
        this.audioEngine.playTrackAtIndex(trackIndex);
    }

    toggleTrackPlayback(trackId) {
        const currentTrack = this.audioEngine.getCurrentTrack();
        
        if (currentTrack && currentTrack.id === trackId) {
            // Same track - toggle play/pause
            if (this.audioEngine.isPlayingState()) {
                this.audioEngine.pause();
            } else {
                this.audioEngine.play();
            }
        } else {
            // Different track - play it
            this.playTrack(trackId);
        }
    }

    // Track actions
    downloadTrack(trackId) {
        const track = this.tracks.find(t => t.id === trackId);
        if (!track || !track.src) return;
        
        const link = document.createElement('a');
        link.href = track.src;
        link.download = `${track.artist} - ${track.title}.mp3`;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // State updates
    updateActiveTrack(track) {
        // Remove active class from all items
        const items = this.playlistContainer.querySelectorAll('.playlist-item');
        items.forEach(item => {
            item.classList.remove('active', 'playing');
            const playBtn = item.querySelector('[data-action="play"] i');
            if (playBtn) {
                playBtn.className = 'fas fa-play';
            }
        });
        
        // Add active class to current track
        if (track) {
            const activeItem = this.playlistContainer.querySelector(`[data-track-id="${track.id}"]`);
            if (activeItem) {
                activeItem.classList.add('active');
                const playBtn = activeItem.querySelector('[data-action="play"] i');
                if (playBtn) {
                    playBtn.className = 'fas fa-pause';
                }
            }
        }
    }

    updatePlayingState(isPlaying) {
        const currentTrack = this.audioEngine.getCurrentTrack();
        if (!currentTrack) return;
        
        const activeItem = this.playlistContainer.querySelector(`[data-track-id="${currentTrack.id}"]`);
        if (activeItem) {
            activeItem.classList.toggle('playing', isPlaying);
            const playBtn = activeItem.querySelector('[data-action="play"] i');
            if (playBtn) {
                playBtn.className = `fas ${isPlaying ? 'fa-pause' : 'fa-play'}`;
            }
        }
    }

    // Playlist persistence
    savePlaylist() {
        if (this.tracks.length === 0) {
            alert('No tracks to save');
            return;
        }
        
        const playlistName = prompt('Enter playlist name:', 'My Playlist');
        if (!playlistName) return;
        
        const playlistData = {
            name: playlistName,
            tracks: this.tracks,
            created: new Date().toISOString()
        };
        
        // Save to localStorage
        const savedPlaylists = JSON.parse(localStorage.getItem('mp3Player_playlists') || '[]');
        savedPlaylists.push(playlistData);
        localStorage.setItem('mp3Player_playlists', JSON.stringify(savedPlaylists));
        
        alert(`Playlist "${playlistName}" saved successfully!`);
        this.dispatchEvent('playlistSaved', { playlist: playlistData });
    }

    loadPlaylist(playlistData) {
        this.tracks = [...playlistData.tracks];
        this.filterTracks();
        this.updateAudioEnginePlaylist();
        
        this.dispatchEvent('playlistLoaded', { playlist: playlistData });
    }

    // Utility methods
    updateAudioEnginePlaylist() {
        this.audioEngine.setPlaylist(this.tracks);
    }

    generateTrackId() {
        return 'track_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    formatDuration(seconds) {
        if (!seconds || isNaN(seconds)) return '0:00';
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Getters
    getTracks() {
        return this.tracks;
    }

    getFilteredTracks() {
        return this.filteredTracks;
    }

    getTrackCount() {
        return this.tracks.length;
    }

    // Event system
    dispatchEvent(eventName, data = {}) {
        const event = new CustomEvent(`playlistManager:${eventName}`, {
            detail: data
        });
        document.dispatchEvent(event);
    }
}

// Export for use in other modules
window.PlaylistManager = PlaylistManager;
