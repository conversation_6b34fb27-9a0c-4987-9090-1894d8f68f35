/**
 * Audio Engine - Core audio player functionality
 * Handles audio playback, controls, and state management
 */

class AudioEngine {
    constructor() {
        this.audio = document.getElementById('audioPlayer');
        this.currentTrack = null;
        this.playlist = [];
        this.currentIndex = 0;
        this.isPlaying = false;
        this.isMuted = false;
        this.volume = 1;
        this.playbackRate = 1;
        this.isShuffled = false;
        this.repeatMode = 'none'; // 'none', 'one', 'all'
        this.originalPlaylist = [];
        
        // Audio context for advanced features
        this.audioContext = null;
        this.analyser = null;
        this.source = null;
        
        this.initializeAudio();
        this.bindEvents();
    }

    initializeAudio() {
        // Set initial volume
        this.audio.volume = this.volume;
        this.audio.playbackRate = this.playbackRate;
        
        // Initialize Web Audio API for visualizations
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = 256;
            
            // Connect audio element to analyser when a track is loaded
            this.audio.addEventListener('loadstart', () => {
                if (!this.source) {
                    this.source = this.audioContext.createMediaElementSource(this.audio);
                    this.source.connect(this.analyser);
                    this.analyser.connect(this.audioContext.destination);
                }
            });
        } catch (error) {
            console.warn('Web Audio API not supported:', error);
        }
    }

    bindEvents() {
        // Audio element events
        this.audio.addEventListener('loadedmetadata', () => this.onLoadedMetadata());
        this.audio.addEventListener('timeupdate', () => this.onTimeUpdate());
        this.audio.addEventListener('ended', () => this.onTrackEnded());
        this.audio.addEventListener('play', () => this.onPlay());
        this.audio.addEventListener('pause', () => this.onPause());
        this.audio.addEventListener('error', (e) => this.onError(e));
        this.audio.addEventListener('loadstart', () => this.onLoadStart());
        this.audio.addEventListener('canplay', () => this.onCanPlay());
        this.audio.addEventListener('waiting', () => this.onWaiting());
        this.audio.addEventListener('volumechange', () => this.onVolumeChange());
    }

    // Event handlers
    onLoadedMetadata() {
        this.dispatchEvent('metadataLoaded', {
            duration: this.audio.duration,
            track: this.currentTrack
        });
    }

    onTimeUpdate() {
        this.dispatchEvent('timeUpdate', {
            currentTime: this.audio.currentTime,
            duration: this.audio.duration,
            progress: this.audio.currentTime / this.audio.duration
        });
    }

    onTrackEnded() {
        this.dispatchEvent('trackEnded', { track: this.currentTrack });
        this.handleTrackEnd();
    }

    onPlay() {
        this.isPlaying = true;
        this.dispatchEvent('play', { track: this.currentTrack });
        
        // Resume audio context if suspended
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }

    onPause() {
        this.isPlaying = false;
        this.dispatchEvent('pause', { track: this.currentTrack });
    }

    onError(event) {
        console.error('Audio error:', event);
        this.dispatchEvent('error', { 
            error: event,
            track: this.currentTrack 
        });
    }

    onLoadStart() {
        this.dispatchEvent('loadStart', { track: this.currentTrack });
    }

    onCanPlay() {
        this.dispatchEvent('canPlay', { track: this.currentTrack });
    }

    onWaiting() {
        this.dispatchEvent('waiting', { track: this.currentTrack });
    }

    onVolumeChange() {
        this.volume = this.audio.volume;
        this.isMuted = this.audio.muted;
        this.dispatchEvent('volumeChange', { 
            volume: this.volume,
            muted: this.isMuted 
        });
    }

    // Core playback methods
    async play() {
        try {
            await this.audio.play();
            return true;
        } catch (error) {
            console.error('Play failed:', error);
            this.dispatchEvent('playError', { error });
            return false;
        }
    }

    pause() {
        this.audio.pause();
    }

    stop() {
        this.pause();
        this.audio.currentTime = 0;
    }

    // Track management
    loadTrack(track, autoPlay = false) {
        if (!track || !track.src) {
            console.error('Invalid track data');
            return false;
        }

        this.currentTrack = track;
        this.audio.src = track.src;
        
        this.dispatchEvent('trackLoaded', { track });
        
        if (autoPlay) {
            // Small delay to ensure metadata is loaded
            setTimeout(() => this.play(), 100);
        }
        
        return true;
    }

    setPlaylist(playlist, startIndex = 0) {
        this.playlist = [...playlist];
        this.originalPlaylist = [...playlist];
        this.currentIndex = startIndex;
        
        if (this.isShuffled) {
            this.shufflePlaylist();
        }
        
        this.dispatchEvent('playlistChanged', { 
            playlist: this.playlist,
            currentIndex: this.currentIndex 
        });
    }

    // Navigation methods
    next() {
        if (this.playlist.length === 0) return false;
        
        if (this.repeatMode === 'one') {
            this.seek(0);
            this.play();
            return true;
        }
        
        this.currentIndex++;
        
        if (this.currentIndex >= this.playlist.length) {
            if (this.repeatMode === 'all') {
                this.currentIndex = 0;
            } else {
                this.currentIndex = this.playlist.length - 1;
                return false;
            }
        }
        
        const track = this.playlist[this.currentIndex];
        this.loadTrack(track, this.isPlaying);
        return true;
    }

    previous() {
        if (this.playlist.length === 0) return false;
        
        // If more than 3 seconds played, restart current track
        if (this.audio.currentTime > 3) {
            this.seek(0);
            return true;
        }
        
        this.currentIndex--;
        
        if (this.currentIndex < 0) {
            if (this.repeatMode === 'all') {
                this.currentIndex = this.playlist.length - 1;
            } else {
                this.currentIndex = 0;
                return false;
            }
        }
        
        const track = this.playlist[this.currentIndex];
        this.loadTrack(track, this.isPlaying);
        return true;
    }

    playTrackAtIndex(index) {
        if (index < 0 || index >= this.playlist.length) return false;
        
        this.currentIndex = index;
        const track = this.playlist[this.currentIndex];
        this.loadTrack(track, true);
        return true;
    }

    // Control methods
    seek(time) {
        if (this.audio.duration) {
            this.audio.currentTime = Math.max(0, Math.min(time, this.audio.duration));
        }
    }

    seekToPercent(percent) {
        if (this.audio.duration) {
            const time = (percent / 100) * this.audio.duration;
            this.seek(time);
        }
    }

    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        this.audio.volume = this.volume;
    }

    mute() {
        this.audio.muted = true;
    }

    unmute() {
        this.audio.muted = false;
    }

    toggleMute() {
        this.audio.muted = !this.audio.muted;
    }

    setPlaybackRate(rate) {
        this.playbackRate = Math.max(0.25, Math.min(4, rate));
        this.audio.playbackRate = this.playbackRate;
        this.dispatchEvent('playbackRateChanged', { rate: this.playbackRate });
    }

    // Playlist manipulation
    shuffle() {
        if (this.playlist.length <= 1) return;
        
        this.isShuffled = true;
        const currentTrack = this.playlist[this.currentIndex];
        
        // Fisher-Yates shuffle
        for (let i = this.playlist.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.playlist[i], this.playlist[j]] = [this.playlist[j], this.playlist[i]];
        }
        
        // Ensure current track stays at current position
        const newIndex = this.playlist.findIndex(track => track.id === currentTrack.id);
        if (newIndex !== -1 && newIndex !== this.currentIndex) {
            [this.playlist[this.currentIndex], this.playlist[newIndex]] = 
            [this.playlist[newIndex], this.playlist[this.currentIndex]];
        }
        
        this.dispatchEvent('playlistShuffled', { playlist: this.playlist });
    }

    unshuffle() {
        this.isShuffled = false;
        const currentTrack = this.playlist[this.currentIndex];
        this.playlist = [...this.originalPlaylist];
        
        // Find current track in original playlist
        this.currentIndex = this.playlist.findIndex(track => track.id === currentTrack.id);
        if (this.currentIndex === -1) this.currentIndex = 0;
        
        this.dispatchEvent('playlistUnshuffled', { playlist: this.playlist });
    }

    toggleShuffle() {
        if (this.isShuffled) {
            this.unshuffle();
        } else {
            this.shuffle();
        }
    }

    setRepeatMode(mode) {
        const validModes = ['none', 'one', 'all'];
        if (validModes.includes(mode)) {
            this.repeatMode = mode;
            this.dispatchEvent('repeatModeChanged', { mode });
        }
    }

    toggleRepeat() {
        const modes = ['none', 'one', 'all'];
        const currentIndex = modes.indexOf(this.repeatMode);
        const nextIndex = (currentIndex + 1) % modes.length;
        this.setRepeatMode(modes[nextIndex]);
    }

    // Handle track end based on repeat mode
    handleTrackEnd() {
        switch (this.repeatMode) {
            case 'one':
                this.seek(0);
                this.play();
                break;
            case 'all':
                if (!this.next()) {
                    // If we can't go to next (end of playlist), start from beginning
                    this.currentIndex = 0;
                    const track = this.playlist[this.currentIndex];
                    this.loadTrack(track, true);
                }
                break;
            default:
                this.next();
                break;
        }
    }

    // Utility methods
    getCurrentTrack() {
        return this.currentTrack;
    }

    getCurrentIndex() {
        return this.currentIndex;
    }

    getPlaylist() {
        return this.playlist;
    }

    getDuration() {
        return this.audio.duration || 0;
    }

    getCurrentTime() {
        return this.audio.currentTime || 0;
    }

    getVolume() {
        return this.volume;
    }

    isMutedState() {
        return this.isMuted;
    }

    getPlaybackRate() {
        return this.playbackRate;
    }

    getRepeatMode() {
        return this.repeatMode;
    }

    isShuffledState() {
        return this.isShuffled;
    }

    isPlayingState() {
        return this.isPlaying;
    }

    // Audio analysis for visualizations
    getFrequencyData() {
        if (!this.analyser) return null;
        
        const bufferLength = this.analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        this.analyser.getByteFrequencyData(dataArray);
        return dataArray;
    }

    getTimeDomainData() {
        if (!this.analyser) return null;
        
        const bufferLength = this.analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        this.analyser.getByteTimeDomainData(dataArray);
        return dataArray;
    }

    // Event system
    dispatchEvent(eventName, data = {}) {
        const event = new CustomEvent(`audioEngine:${eventName}`, {
            detail: data
        });
        document.dispatchEvent(event);
    }

    // Cleanup
    destroy() {
        this.pause();
        this.audio.src = '';
        
        if (this.audioContext) {
            this.audioContext.close();
        }
        
        this.playlist = [];
        this.currentTrack = null;
    }
}

// Export for use in other modules
window.AudioEngine = AudioEngine;
