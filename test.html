<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MP3 Audio Player - Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 MP3 Audio Player - Test Suite</h1>
    
    <div class="test-section">
        <h2>Browser Compatibility Tests</h2>
        <div id="browser-tests"></div>
        <button onclick="runBrowserTests()">Run Browser Tests</button>
    </div>
    
    <div class="test-section">
        <h2>Audio API Tests</h2>
        <div id="audio-tests"></div>
        <button onclick="runAudioTests()">Run Audio Tests</button>
    </div>
    
    <div class="test-section">
        <h2>Component Loading Tests</h2>
        <div id="component-tests"></div>
        <button onclick="runComponentTests()">Run Component Tests</button>
    </div>
    
    <div class="test-section">
        <h2>Feature Tests</h2>
        <div id="feature-tests"></div>
        <button onclick="runFeatureTests()">Run Feature Tests</button>
    </div>
    
    <div class="test-section">
        <h2>Console Output</h2>
        <div id="console-output"></div>
        <button onclick="clearConsole()">Clear Console</button>
        <button onclick="window.open('index.html', '_blank')">Open Main Player</button>
    </div>

    <script>
        // Console capture
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };
        
        const consoleOutput = document.getElementById('console-output');
        
        function logToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type.toUpperCase()}: ${args.join(' ')}\n`;
            consoleOutput.textContent += message;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            originalConsole[type](...args);
        }
        
        console.log = (...args) => logToConsole('log', ...args);
        console.error = (...args) => logToConsole('error', ...args);
        console.warn = (...args) => logToConsole('warn', ...args);
        console.info = (...args) => logToConsole('info', ...args);
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        function addTestResult(containerId, testName, passed, message = '') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `test-result ${passed ? 'test-pass' : 'test-fail'}`;
            result.innerHTML = `
                <strong>${testName}</strong>: ${passed ? '✅ PASS' : '❌ FAIL'}
                ${message ? `<br><small>${message}</small>` : ''}
            `;
            container.appendChild(result);
        }
        
        function addTestInfo(containerId, message) {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = 'test-result test-info';
            result.innerHTML = `<strong>ℹ️ Info:</strong> ${message}`;
            container.appendChild(result);
        }
        
        function runBrowserTests() {
            const container = document.getElementById('browser-tests');
            container.innerHTML = '';
            
            // Test HTML5 Audio support
            const audio = document.createElement('audio');
            addTestResult('browser-tests', 'HTML5 Audio Support', !!audio.canPlayType, 
                audio.canPlayType ? 'Audio element is supported' : 'Audio element not supported');
            
            // Test MP3 support
            const mp3Support = audio.canPlayType('audio/mpeg');
            addTestResult('browser-tests', 'MP3 Format Support', !!mp3Support, 
                `MP3 support: ${mp3Support || 'Not supported'}`);
            
            // Test Web Audio API
            const audioContext = window.AudioContext || window.webkitAudioContext;
            addTestResult('browser-tests', 'Web Audio API Support', !!audioContext, 
                audioContext ? 'Web Audio API is available' : 'Web Audio API not supported');
            
            // Test File API
            addTestResult('browser-tests', 'File API Support', !!(window.File && window.FileReader), 
                'Required for drag & drop uploads');
            
            // Test Local Storage
            addTestResult('browser-tests', 'Local Storage Support', !!window.localStorage, 
                'Required for saving preferences');
            
            // Test Canvas
            const canvas = document.createElement('canvas');
            addTestResult('browser-tests', 'Canvas Support', !!(canvas.getContext && canvas.getContext('2d')), 
                'Required for waveform visualization');
            
            addTestInfo('browser-tests', `Browser: ${navigator.userAgent}`);
        }
        
        function runAudioTests() {
            const container = document.getElementById('audio-tests');
            container.innerHTML = '';
            
            try {
                // Test audio context creation
                const AudioContext = window.AudioContext || window.webkitAudioContext;
                const audioContext = new AudioContext();
                addTestResult('audio-tests', 'Audio Context Creation', true, 
                    `Sample rate: ${audioContext.sampleRate}Hz, State: ${audioContext.state}`);
                
                // Test analyser node
                const analyser = audioContext.createAnalyser();
                addTestResult('audio-tests', 'Analyser Node Creation', true, 
                    `FFT Size: ${analyser.fftSize}, Frequency bins: ${analyser.frequencyBinCount}`);
                
                // Test gain node
                const gainNode = audioContext.createGain();
                addTestResult('audio-tests', 'Gain Node Creation', true, 
                    `Initial gain: ${gainNode.gain.value}`);
                
                audioContext.close();
                
            } catch (error) {
                addTestResult('audio-tests', 'Audio Context Creation', false, error.message);
            }
            
            // Test media session API
            addTestResult('audio-tests', 'Media Session API', !!navigator.mediaSession, 
                'Enables browser media controls integration');
        }
        
        function runComponentTests() {
            const container = document.getElementById('component-tests');
            container.innerHTML = '';
            
            // Test if main HTML elements exist
            const requiredElements = [
                'audioPlayer', 'albumArt', 'trackTitle', 'playPauseBtn', 
                'progressBar', 'volumeBar', 'playlistContainer', 'waveformCanvas'
            ];
            
            let elementsFound = 0;
            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                const exists = !!element;
                if (exists) elementsFound++;
                addTestResult('component-tests', `Element #${id}`, exists, 
                    exists ? `Found: ${element.tagName}` : 'Element not found in DOM');
            });
            
            addTestInfo('component-tests', `Found ${elementsFound}/${requiredElements.length} required elements`);
            
            // Test CSS loading
            const styles = getComputedStyle(document.documentElement);
            const primaryColor = styles.getPropertyValue('--primary-color').trim();
            addTestResult('component-tests', 'CSS Variables', !!primaryColor, 
                primaryColor ? `Primary color: ${primaryColor}` : 'CSS custom properties not loaded');
        }
        
        function runFeatureTests() {
            const container = document.getElementById('feature-tests');
            container.innerHTML = '';
            
            // Test drag and drop API
            addTestResult('feature-tests', 'Drag & Drop API', 
                !!(window.DataTransfer && window.FileList), 
                'Required for file uploads');
            
            // Test URL.createObjectURL
            addTestResult('feature-tests', 'Object URL Support', !!window.URL.createObjectURL, 
                'Required for local file playback');
            
            // Test Fetch API
            addTestResult('feature-tests', 'Fetch API', !!window.fetch, 
                'Required for loading audio files');
            
            // Test Promise support
            addTestResult('feature-tests', 'Promise Support', !!window.Promise, 
                'Required for async operations');
            
            // Test ES6 features
            try {
                eval('const test = () => {}; class Test {}');
                addTestResult('feature-tests', 'ES6 Support', true, 
                    'Arrow functions and classes supported');
            } catch (error) {
                addTestResult('feature-tests', 'ES6 Support', false, 
                    'Modern JavaScript features not supported');
            }
            
            // Test responsive design
            const isMobile = window.innerWidth <= 768;
            addTestInfo('feature-tests', `Screen size: ${window.innerWidth}x${window.innerHeight} (${isMobile ? 'Mobile' : 'Desktop'} layout)`);
        }
        
        // Auto-run basic tests on load
        window.addEventListener('load', () => {
            console.log('🧪 Test page loaded');
            console.log('👉 Click the test buttons to run compatibility checks');
            
            // Run browser tests automatically
            setTimeout(() => {
                runBrowserTests();
            }, 500);
        });
        
        // Error handling
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
        });
        
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
        });
    </script>
</body>
</html>
