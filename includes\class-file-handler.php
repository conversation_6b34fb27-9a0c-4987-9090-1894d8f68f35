<?php
/**
 * File Handler Class
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * MP3 Audio Player Pro File Handler Class
 */
class MP3_Audio_Player_Pro_File_Handler {
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Allowed file types
     */
    private $allowed_types = array('mp3', 'wav', 'ogg', 'm4a', 'aac', 'flac');
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_ajax_mp3_player_upload_file', array($this, 'handle_upload'));
        add_action('wp_ajax_mp3_player_save_playlist', array($this, 'handle_save_playlist'));
        add_action('wp_ajax_mp3_player_load_playlist', array($this, 'handle_load_playlist'));
        add_action('wp_ajax_mp3_player_delete_track', array($this, 'handle_delete_track'));
        
        // Add custom upload directory
        add_filter('upload_dir', array($this, 'custom_upload_dir'));
    }
    
    /**
     * Handle file upload via AJAX
     */
    public function handle_upload() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'mp3_player_frontend_nonce')) {
            wp_die(__('Security check failed.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        // Check user permissions
        if (!current_user_can('upload_files')) {
            wp_die(__('You do not have permission to upload files.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        // Check if file was uploaded
        if (!isset($_FILES['audio_file'])) {
            wp_send_json_error(__('No file uploaded.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        $file = $_FILES['audio_file'];
        
        // Validate file
        $validation = $this->validate_audio_file($file);
        if (is_wp_error($validation)) {
            wp_send_json_error($validation->get_error_message());
        }
        
        // Upload file
        $upload_result = $this->upload_audio_file($file);
        if (is_wp_error($upload_result)) {
            wp_send_json_error($upload_result->get_error_message());
        }
        
        // Extract metadata
        $metadata = $this->extract_metadata($upload_result['file']);
        
        // Save to database
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        $track_data = array(
            'title' => $metadata['title'] ?: pathinfo($file['name'], PATHINFO_FILENAME),
            'artist' => $metadata['artist'] ?: '',
            'album' => $metadata['album'] ?: '',
            'duration' => $metadata['duration'] ?: 0,
            'file_url' => $upload_result['url'],
            'file_path' => $upload_result['file'],
            'attachment_id' => $upload_result['attachment_id'],
            'artwork_url' => $metadata['artwork_url'] ?: '',
            'genre' => $metadata['genre'] ?: '',
            'year' => $metadata['year'] ?: '',
            'track_number' => $metadata['track_number'] ?: '',
            'file_size' => $file['size'],
            'file_format' => $metadata['format'] ?: '',
            'bitrate' => $metadata['bitrate'] ?: '',
            'sample_rate' => $metadata['sample_rate'] ?: '',
            'metadata' => $metadata,
            'user_id' => get_current_user_id(),
        );
        
        $track_id = $db->create_track($track_data);
        
        if (!$track_id) {
            wp_send_json_error(__('Failed to save track to database.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        // Get the created track
        $track = $db->get_track($track_id);
        
        wp_send_json_success(array(
            'track' => $this->format_track_for_frontend($track),
            'message' => __('File uploaded successfully!', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
        ));
    }
    
    /**
     * Validate audio file
     */
    private function validate_audio_file($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return new WP_Error('upload_error', __('File upload error.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        // Check file size (default: 50MB)
        $max_size = apply_filters('mp3_player_max_file_size', 50 * 1024 * 1024);
        if ($file['size'] > $max_size) {
            return new WP_Error('file_too_large', sprintf(
                __('File is too large. Maximum size is %s.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                size_format($max_size)
            ));
        }
        
        // Check file extension
        $file_ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_ext, $this->allowed_types)) {
            return new WP_Error('invalid_file_type', sprintf(
                __('Invalid file type. Allowed types: %s', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                implode(', ', $this->allowed_types)
            ));
        }
        
        // Check MIME type
        $allowed_mimes = array(
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'ogg' => 'audio/ogg',
            'm4a' => 'audio/mp4',
            'aac' => 'audio/aac',
            'flac' => 'audio/flac',
        );
        
        $file_type = wp_check_filetype($file['name'], $allowed_mimes);
        if (!$file_type['type']) {
            return new WP_Error('invalid_mime_type', __('Invalid file type.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        return true;
    }
    
    /**
     * Upload audio file
     */
    private function upload_audio_file($file) {
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        
        // Set custom upload directory
        add_filter('upload_dir', array($this, 'custom_upload_dir'));
        
        // Handle the upload
        $upload_overrides = array(
            'test_form' => false,
            'test_size' => true,
            'test_upload' => true,
        );
        
        $uploaded_file = wp_handle_upload($file, $upload_overrides);
        
        // Remove custom upload directory filter
        remove_filter('upload_dir', array($this, 'custom_upload_dir'));
        
        if (isset($uploaded_file['error'])) {
            return new WP_Error('upload_failed', $uploaded_file['error']);
        }
        
        // Create attachment
        $attachment_data = array(
            'post_mime_type' => $uploaded_file['type'],
            'post_title' => sanitize_file_name(pathinfo($file['name'], PATHINFO_FILENAME)),
            'post_content' => '',
            'post_status' => 'inherit',
        );
        
        $attachment_id = wp_insert_attachment($attachment_data, $uploaded_file['file']);
        
        if (is_wp_error($attachment_id)) {
            return $attachment_id;
        }
        
        // Generate attachment metadata
        $attachment_metadata = wp_generate_attachment_metadata($attachment_id, $uploaded_file['file']);
        wp_update_attachment_metadata($attachment_id, $attachment_metadata);
        
        return array(
            'file' => $uploaded_file['file'],
            'url' => $uploaded_file['url'],
            'type' => $uploaded_file['type'],
            'attachment_id' => $attachment_id,
        );
    }
    
    /**
     * Custom upload directory for audio files
     */
    public function custom_upload_dir($upload) {
        $upload['subdir'] = '/mp3-audio-player-pro' . $upload['subdir'];
        $upload['path'] = $upload['basedir'] . $upload['subdir'];
        $upload['url'] = $upload['baseurl'] . $upload['subdir'];
        
        return $upload;
    }
    
    /**
     * Extract metadata from audio file
     */
    private function extract_metadata($file_path) {
        $metadata = array(
            'title' => '',
            'artist' => '',
            'album' => '',
            'duration' => 0,
            'genre' => '',
            'year' => '',
            'track_number' => '',
            'format' => '',
            'bitrate' => '',
            'sample_rate' => '',
            'artwork_url' => '',
        );
        
        // Try to get duration using getID3 if available
        if (class_exists('getID3')) {
            $getID3 = new getID3();
            $file_info = $getID3->analyze($file_path);
            
            if (isset($file_info['playtime_seconds'])) {
                $metadata['duration'] = (int) $file_info['playtime_seconds'];
            }
            
            if (isset($file_info['tags']['id3v2'])) {
                $tags = $file_info['tags']['id3v2'];
                $metadata['title'] = isset($tags['title'][0]) ? $tags['title'][0] : '';
                $metadata['artist'] = isset($tags['artist'][0]) ? $tags['artist'][0] : '';
                $metadata['album'] = isset($tags['album'][0]) ? $tags['album'][0] : '';
                $metadata['genre'] = isset($tags['genre'][0]) ? $tags['genre'][0] : '';
                $metadata['year'] = isset($tags['year'][0]) ? $tags['year'][0] : '';
                $metadata['track_number'] = isset($tags['track_number'][0]) ? $tags['track_number'][0] : '';
            }
            
            if (isset($file_info['audio'])) {
                $audio = $file_info['audio'];
                $metadata['format'] = isset($audio['dataformat']) ? $audio['dataformat'] : '';
                $metadata['bitrate'] = isset($audio['bitrate']) ? $audio['bitrate'] . ' kbps' : '';
                $metadata['sample_rate'] = isset($audio['sample_rate']) ? $audio['sample_rate'] . ' Hz' : '';
            }
        }
        
        return $metadata;
    }
    
    /**
     * Handle save playlist AJAX
     */
    public function handle_save_playlist() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'mp3_player_frontend_nonce')) {
            wp_die(__('Security check failed.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        // Check user permissions
        if (!current_user_can('edit_posts')) {
            wp_die(__('You do not have permission to save playlists.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        $playlist_name = sanitize_text_field($_POST['playlist_name']);
        $track_ids = array_map('intval', $_POST['track_ids']);
        
        if (empty($playlist_name)) {
            wp_send_json_error(__('Playlist name is required.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        
        // Create playlist
        $playlist_id = $db->create_playlist(array(
            'name' => $playlist_name,
            'user_id' => get_current_user_id(),
        ));
        
        if (!$playlist_id) {
            wp_send_json_error(__('Failed to create playlist.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        // Add tracks to playlist
        foreach ($track_ids as $index => $track_id) {
            $db->add_track_to_playlist($playlist_id, $track_id, $index + 1);
        }
        
        wp_send_json_success(array(
            'playlist_id' => $playlist_id,
            'message' => __('Playlist saved successfully!', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
        ));
    }
    
    /**
     * Handle load playlist AJAX
     */
    public function handle_load_playlist() {
        $playlist_id = intval($_POST['playlist_id']);
        
        if (!$playlist_id) {
            wp_send_json_error(__('Invalid playlist ID.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        $playlist = $db->get_playlist($playlist_id);
        
        if (!$playlist) {
            wp_send_json_error(__('Playlist not found.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        // Check permissions
        if (!$playlist->is_public && $playlist->user_id != get_current_user_id() && !current_user_can('edit_others_posts')) {
            wp_send_json_error(__('You do not have permission to access this playlist.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        $tracks = $db->get_tracks(null, $playlist_id);
        $formatted_tracks = array();
        
        foreach ($tracks as $track) {
            $formatted_tracks[] = $this->format_track_for_frontend($track);
        }
        
        wp_send_json_success(array(
            'playlist' => array(
                'id' => $playlist->id,
                'name' => $playlist->name,
                'description' => $playlist->description,
            ),
            'tracks' => $formatted_tracks,
        ));
    }
    
    /**
     * Handle delete track AJAX
     */
    public function handle_delete_track() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'mp3_player_frontend_nonce')) {
            wp_die(__('Security check failed.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        $track_id = intval($_POST['track_id']);
        
        if (!$track_id) {
            wp_send_json_error(__('Invalid track ID.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        $track = $db->get_track($track_id);
        
        if (!$track) {
            wp_send_json_error(__('Track not found.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        // Check permissions
        if ($track->user_id != get_current_user_id() && !current_user_can('edit_others_posts')) {
            wp_send_json_error(__('You do not have permission to delete this track.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        // Delete file if it exists
        if ($track->attachment_id) {
            wp_delete_attachment($track->attachment_id, true);
        } elseif ($track->file_path && file_exists($track->file_path)) {
            unlink($track->file_path);
        }
        
        // Delete from database
        $success = $db->delete_track($track_id);
        
        if (!$success) {
            wp_send_json_error(__('Failed to delete track.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN));
        }
        
        wp_send_json_success(array(
            'message' => __('Track deleted successfully!', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
        ));
    }
    
    /**
     * Format track for frontend
     */
    private function format_track_for_frontend($track) {
        $metadata = !empty($track->metadata) ? unserialize($track->metadata) : array();
        
        return array(
            'id' => $track->id,
            'title' => $track->title,
            'artist' => $track->artist,
            'album' => $track->album,
            'duration' => intval($track->duration),
            'src' => $track->file_url,
            'artwork' => $track->artwork_url,
            'genre' => $track->genre,
            'year' => $track->year,
            'trackNumber' => $track->track_number,
            'fileSize' => intval($track->file_size),
            'format' => $track->file_format,
            'bitrate' => $track->bitrate,
            'sampleRate' => $track->sample_rate,
            'metadata' => $metadata,
        );
    }
}
