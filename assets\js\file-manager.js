/**
 * File Manager - Handles file uploads and ID3 tag extraction
 * Manages drag-and-drop uploads, file processing, and metadata extraction
 */

class FileManager {
    constructor(playlistManager) {
        this.playlistManager = playlistManager;
        
        // DOM elements
        this.uploadBtn = document.getElementById('uploadBtn');
        this.uploadModal = document.getElementById('uploadModal');
        this.modalClose = document.getElementById('modalClose');
        this.uploadArea = document.getElementById('uploadArea');
        this.fileInput = document.getElementById('fileInput');
        this.browseBtn = document.getElementById('browseBtn');
        this.uploadProgress = document.getElementById('uploadProgress');
        this.uploadProgressFill = document.getElementById('uploadProgressFill');
        this.uploadStatus = document.getElementById('uploadStatus');
        
        // State
        this.isUploading = false;
        this.supportedFormats = ['audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/m4a'];
        
        this.bindEvents();
    }

    bindEvents() {
        // Modal controls
        this.uploadBtn.addEventListener('click', () => this.openUploadModal());
        this.modalClose.addEventListener('click', () => this.closeUploadModal());
        this.uploadModal.addEventListener('click', (e) => {
            if (e.target === this.uploadModal) {
                this.closeUploadModal();
            }
        });
        
        // File input
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        this.browseBtn.addEventListener('click', () => this.fileInput.click());
        
        // Drag and drop
        this.uploadArea.addEventListener('click', () => this.fileInput.click());
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        
        // Global drag and drop
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
            if (this.isAudioFile(e.dataTransfer)) {
                this.openUploadModal();
            }
        });
        
        document.addEventListener('drop', (e) => {
            e.preventDefault();
            if (this.isAudioFile(e.dataTransfer)) {
                this.handleDrop(e);
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.uploadModal.classList.contains('active')) {
                this.closeUploadModal();
            }
        });
    }

    // Modal management
    openUploadModal() {
        this.uploadModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeUploadModal() {
        if (this.isUploading) {
            if (!confirm('Upload in progress. Are you sure you want to close?')) {
                return;
            }
        }
        
        this.uploadModal.classList.remove('active');
        document.body.style.overflow = '';
        this.resetUploadState();
    }

    // Drag and drop handlers
    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        
        // Only remove dragover if we're actually leaving the upload area
        if (!this.uploadArea.contains(e.relatedTarget)) {
            this.uploadArea.classList.remove('dragover');
        }
    }

    handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        this.uploadArea.classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files);
        this.processFiles(files);
    }

    // File selection handler
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.processFiles(files);
    }

    // File processing
    async processFiles(files) {
        if (this.isUploading) return;
        
        const audioFiles = files.filter(file => this.isValidAudioFile(file));
        
        if (audioFiles.length === 0) {
            alert('No valid audio files selected. Please select MP3, WAV, OGG, or M4A files.');
            return;
        }
        
        if (audioFiles.length !== files.length) {
            const skipped = files.length - audioFiles.length;
            alert(`${skipped} file(s) skipped. Only audio files are supported.`);
        }
        
        this.isUploading = true;
        this.showUploadProgress();
        
        try {
            await this.uploadFiles(audioFiles);
            this.showUploadSuccess(audioFiles.length);
            
            // Auto-close modal after successful upload
            setTimeout(() => {
                this.closeUploadModal();
            }, 2000);
            
        } catch (error) {
            console.error('Upload failed:', error);
            this.showUploadError(error.message);
        } finally {
            this.isUploading = false;
        }
    }

    async uploadFiles(files) {
        const totalFiles = files.length;
        let processedFiles = 0;
        
        for (const file of files) {
            try {
                this.updateUploadProgress(processedFiles, totalFiles, `Processing ${file.name}...`);
                
                const track = await this.processAudioFile(file);
                this.playlistManager.addTrack(track);
                
                processedFiles++;
                this.updateUploadProgress(processedFiles, totalFiles, 
                    processedFiles === totalFiles ? 'Upload complete!' : `Processed ${processedFiles}/${totalFiles} files`);
                
            } catch (error) {
                console.error(`Failed to process ${file.name}:`, error);
                // Continue with other files
                processedFiles++;
            }
        }
    }

    async processAudioFile(file) {
        // Create object URL for the file
        const src = URL.createObjectURL(file);
        
        // Extract metadata
        const metadata = await this.extractMetadata(file);
        
        // Create track object
        const track = {
            id: this.generateTrackId(),
            title: metadata.title || this.getFileNameWithoutExtension(file.name),
            artist: metadata.artist || 'Unknown Artist',
            album: metadata.album || 'Unknown Album',
            duration: metadata.duration || 0,
            src: src,
            file: file,
            artwork: metadata.artwork || null,
            genre: metadata.genre || '',
            year: metadata.year || '',
            trackNumber: metadata.trackNumber || '',
            size: file.size,
            format: file.type,
            bitrate: metadata.bitrate || '',
            sampleRate: metadata.sampleRate || ''
        };
        
        return track;
    }

    async extractMetadata(file) {
        return new Promise((resolve) => {
            const audio = new Audio();
            const objectUrl = URL.createObjectURL(file);
            
            audio.addEventListener('loadedmetadata', () => {
                const metadata = {
                    duration: audio.duration
                };
                
                URL.revokeObjectURL(objectUrl);
                resolve(metadata);
            });
            
            audio.addEventListener('error', () => {
                URL.revokeObjectURL(objectUrl);
                resolve({});
            });
            
            audio.src = objectUrl;
        });
    }

    // ID3 tag extraction (simplified version)
    async extractID3Tags(file) {
        // This is a simplified implementation
        // In a real application, you'd use a library like jsmediatags
        try {
            const arrayBuffer = await file.arrayBuffer();
            const view = new DataView(arrayBuffer);
            
            // Check for ID3v2 header
            if (view.getUint8(0) === 0x49 && view.getUint8(1) === 0x44 && view.getUint8(2) === 0x33) {
                // ID3v2 tag found - this is a simplified parser
                return this.parseID3v2(view);
            }
            
            return {};
        } catch (error) {
            console.error('Failed to extract ID3 tags:', error);
            return {};
        }
    }

    parseID3v2(view) {
        // Simplified ID3v2 parser
        // In production, use a proper library like jsmediatags
        const metadata = {};
        
        try {
            // Skip ID3v2 header (10 bytes)
            let offset = 10;
            
            // This is a very basic implementation
            // A full implementation would properly parse all frames
            
        } catch (error) {
            console.error('ID3v2 parsing error:', error);
        }
        
        return metadata;
    }

    // UI updates
    showUploadProgress() {
        this.uploadProgress.style.display = 'block';
        this.uploadArea.style.display = 'none';
    }

    updateUploadProgress(current, total, status) {
        const percent = (current / total) * 100;
        this.uploadProgressFill.style.width = `${percent}%`;
        this.uploadStatus.textContent = status;
    }

    showUploadSuccess(fileCount) {
        this.uploadStatus.textContent = `Successfully uploaded ${fileCount} file(s)!`;
        this.uploadStatus.style.color = 'var(--primary-color)';
    }

    showUploadError(message) {
        this.uploadStatus.textContent = `Upload failed: ${message}`;
        this.uploadStatus.style.color = '#ef4444';
    }

    resetUploadState() {
        this.uploadProgress.style.display = 'none';
        this.uploadArea.style.display = 'block';
        this.uploadProgressFill.style.width = '0%';
        this.uploadStatus.textContent = 'Uploading...';
        this.uploadStatus.style.color = '';
        this.fileInput.value = '';
    }

    // Validation
    isValidAudioFile(file) {
        return this.supportedFormats.includes(file.type) || 
               this.hasAudioExtension(file.name);
    }

    hasAudioExtension(filename) {
        const audioExtensions = ['.mp3', '.wav', '.ogg', '.m4a', '.aac', '.flac'];
        const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'));
        return audioExtensions.includes(extension);
    }

    isAudioFile(dataTransfer) {
        if (!dataTransfer.files) return false;
        
        return Array.from(dataTransfer.files).some(file => 
            this.isValidAudioFile(file)
        );
    }

    // Utility methods
    generateTrackId() {
        return 'track_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    getFileNameWithoutExtension(filename) {
        return filename.substring(0, filename.lastIndexOf('.')) || filename;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Cleanup
    cleanup() {
        // Revoke object URLs to prevent memory leaks
        const tracks = this.playlistManager.getTracks();
        tracks.forEach(track => {
            if (track.src && track.src.startsWith('blob:')) {
                URL.revokeObjectURL(track.src);
            }
        });
    }
}

// Export for use in other modules
window.FileManager = FileManager;
