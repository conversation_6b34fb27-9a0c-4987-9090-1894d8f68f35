<?php
/**
 * REST API Class
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * MP3 Audio Player Pro REST API Class
 */
class MP3_Audio_Player_Pro_REST_API {
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Namespace
     */
    private $namespace = 'mp3-audio-player-pro/v1';
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Constructor is private
    }
    
    /**
     * Register REST API routes
     */
    public function register_routes() {
        // Playlists endpoints
        register_rest_route($this->namespace, '/playlists', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_playlists'),
                'permission_callback' => array($this, 'check_read_permission'),
            ),
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array($this, 'create_playlist'),
                'permission_callback' => array($this, 'check_write_permission'),
                'args' => $this->get_playlist_schema(),
            ),
        ));
        
        register_rest_route($this->namespace, '/playlists/(?P<id>\d+)', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_playlist'),
                'permission_callback' => array($this, 'check_read_permission'),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array($this, 'update_playlist'),
                'permission_callback' => array($this, 'check_write_permission'),
                'args' => $this->get_playlist_schema(),
            ),
            array(
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => array($this, 'delete_playlist'),
                'permission_callback' => array($this, 'check_write_permission'),
            ),
        ));
        
        // Tracks endpoints
        register_rest_route($this->namespace, '/tracks', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_tracks'),
                'permission_callback' => array($this, 'check_read_permission'),
            ),
            array(
                'methods' => WP_REST_Server::CREATABLE,
                'callback' => array($this, 'create_track'),
                'permission_callback' => array($this, 'check_write_permission'),
                'args' => $this->get_track_schema(),
            ),
        ));
        
        register_rest_route($this->namespace, '/tracks/(?P<id>\d+)', array(
            array(
                'methods' => WP_REST_Server::READABLE,
                'callback' => array($this, 'get_track'),
                'permission_callback' => array($this, 'check_read_permission'),
            ),
            array(
                'methods' => WP_REST_Server::EDITABLE,
                'callback' => array($this, 'update_track'),
                'permission_callback' => array($this, 'check_write_permission'),
                'args' => $this->get_track_schema(),
            ),
            array(
                'methods' => WP_REST_Server::DELETABLE,
                'callback' => array($this, 'delete_track'),
                'permission_callback' => array($this, 'check_write_permission'),
            ),
        ));
        
        // Upload endpoint
        register_rest_route($this->namespace, '/upload', array(
            'methods' => WP_REST_Server::CREATABLE,
            'callback' => array($this, 'upload_file'),
            'permission_callback' => array($this, 'check_upload_permission'),
        ));
    }
    
    /**
     * Permission callbacks
     */
    public function check_read_permission() {
        return true; // Allow public read access
    }
    
    public function check_write_permission() {
        return current_user_can('edit_posts');
    }
    
    public function check_upload_permission() {
        return current_user_can('upload_files');
    }
    
    /**
     * Playlist endpoints
     */
    public function get_playlists($request) {
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        $user_id = get_current_user_id();
        
        $playlists = $db->get_playlists($user_id, !current_user_can('edit_others_posts'));
        
        $data = array();
        foreach ($playlists as $playlist) {
            $data[] = $this->prepare_playlist_for_response($playlist);
        }
        
        return rest_ensure_response($data);
    }
    
    public function get_playlist($request) {
        $id = (int) $request['id'];
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        
        $playlist = $db->get_playlist($id);
        
        if (!$playlist) {
            return new WP_Error('playlist_not_found', __('Playlist not found.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), array('status' => 404));
        }
        
        // Check permissions
        if (!current_user_can('edit_others_posts') && $playlist->user_id != get_current_user_id() && !$playlist->is_public) {
            return new WP_Error('playlist_forbidden', __('You do not have permission to access this playlist.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), array('status' => 403));
        }
        
        return rest_ensure_response($this->prepare_playlist_for_response($playlist));
    }
    
    public function create_playlist($request) {
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        
        $data = array(
            'name' => sanitize_text_field($request['name']),
            'description' => sanitize_textarea_field($request['description']),
            'user_id' => get_current_user_id(),
            'is_public' => (bool) $request['is_public'],
            'settings' => $request['settings'],
        );
        
        $playlist_id = $db->create_playlist($data);
        
        if (!$playlist_id) {
            return new WP_Error('playlist_create_failed', __('Failed to create playlist.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), array('status' => 500));
        }
        
        $playlist = $db->get_playlist($playlist_id);
        return rest_ensure_response($this->prepare_playlist_for_response($playlist));
    }
    
    public function update_playlist($request) {
        $id = (int) $request['id'];
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        
        $playlist = $db->get_playlist($id);
        
        if (!$playlist) {
            return new WP_Error('playlist_not_found', __('Playlist not found.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), array('status' => 404));
        }
        
        // Check permissions
        if (!current_user_can('edit_others_posts') && $playlist->user_id != get_current_user_id()) {
            return new WP_Error('playlist_forbidden', __('You do not have permission to edit this playlist.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), array('status' => 403));
        }
        
        $data = array();
        
        if (isset($request['name'])) {
            $data['name'] = sanitize_text_field($request['name']);
        }
        
        if (isset($request['description'])) {
            $data['description'] = sanitize_textarea_field($request['description']);
        }
        
        if (isset($request['is_public'])) {
            $data['is_public'] = (bool) $request['is_public'];
        }
        
        if (isset($request['settings'])) {
            $data['settings'] = $request['settings'];
        }
        
        $success = $db->update_playlist($id, $data);
        
        if (!$success) {
            return new WP_Error('playlist_update_failed', __('Failed to update playlist.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), array('status' => 500));
        }
        
        $updated_playlist = $db->get_playlist($id);
        return rest_ensure_response($this->prepare_playlist_for_response($updated_playlist));
    }
    
    public function delete_playlist($request) {
        $id = (int) $request['id'];
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        
        $playlist = $db->get_playlist($id);
        
        if (!$playlist) {
            return new WP_Error('playlist_not_found', __('Playlist not found.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), array('status' => 404));
        }
        
        // Check permissions
        if (!current_user_can('edit_others_posts') && $playlist->user_id != get_current_user_id()) {
            return new WP_Error('playlist_forbidden', __('You do not have permission to delete this playlist.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), array('status' => 403));
        }
        
        $success = $db->delete_playlist($id);
        
        if (!$success) {
            return new WP_Error('playlist_delete_failed', __('Failed to delete playlist.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), array('status' => 500));
        }
        
        return rest_ensure_response(array('deleted' => true));
    }
    
    /**
     * Track endpoints
     */
    public function get_tracks($request) {
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        $user_id = get_current_user_id();
        $playlist_id = $request->get_param('playlist');
        
        $tracks = $db->get_tracks($user_id, $playlist_id);
        
        $data = array();
        foreach ($tracks as $track) {
            $data[] = $this->prepare_track_for_response($track);
        }
        
        return rest_ensure_response($data);
    }
    
    public function get_track($request) {
        $id = (int) $request['id'];
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        
        $track = $db->get_track($id);
        
        if (!$track) {
            return new WP_Error('track_not_found', __('Track not found.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), array('status' => 404));
        }
        
        return rest_ensure_response($this->prepare_track_for_response($track));
    }
    
    /**
     * Prepare playlist for response
     */
    private function prepare_playlist_for_response($playlist) {
        $settings = !empty($playlist->settings) ? unserialize($playlist->settings) : array();
        
        return array(
            'id' => (int) $playlist->id,
            'name' => $playlist->name,
            'description' => $playlist->description,
            'user_id' => (int) $playlist->user_id,
            'is_public' => (bool) $playlist->is_public,
            'settings' => $settings,
            'created_at' => $playlist->created_at,
            'updated_at' => $playlist->updated_at,
        );
    }
    
    /**
     * Prepare track for response
     */
    private function prepare_track_for_response($track) {
        $metadata = !empty($track->metadata) ? unserialize($track->metadata) : array();
        
        return array(
            'id' => (int) $track->id,
            'title' => $track->title,
            'artist' => $track->artist,
            'album' => $track->album,
            'duration' => (int) $track->duration,
            'file_url' => $track->file_url,
            'artwork_url' => $track->artwork_url,
            'genre' => $track->genre,
            'year' => $track->year,
            'track_number' => $track->track_number,
            'file_size' => (int) $track->file_size,
            'file_format' => $track->file_format,
            'bitrate' => $track->bitrate,
            'sample_rate' => $track->sample_rate,
            'metadata' => $metadata,
            'user_id' => (int) $track->user_id,
            'created_at' => $track->created_at,
            'updated_at' => $track->updated_at,
        );
    }
    
    /**
     * Get playlist schema
     */
    private function get_playlist_schema() {
        return array(
            'name' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'description' => array(
                'type' => 'string',
                'sanitize_callback' => 'sanitize_textarea_field',
            ),
            'is_public' => array(
                'type' => 'boolean',
            ),
            'settings' => array(
                'type' => 'object',
            ),
        );
    }
    
    /**
     * Get track schema
     */
    private function get_track_schema() {
        return array(
            'title' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'artist' => array(
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'album' => array(
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field',
            ),
            'duration' => array(
                'type' => 'integer',
            ),
            'file_url' => array(
                'required' => true,
                'type' => 'string',
                'format' => 'uri',
            ),
        );
    }
}
