<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MP3 Audio Player - Demo</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .demo-section {
            background: var(--bg-card);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }
        
        .demo-section h2 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-lg);
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }
        
        .feature-card {
            background: var(--bg-tertiary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            text-align: center;
        }
        
        .feature-card i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }
        
        .feature-card h3 {
            margin-bottom: var(--spacing-sm);
            color: var(--text-primary);
        }
        
        .feature-card p {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .demo-controls {
            display: flex;
            gap: var(--spacing-md);
            flex-wrap: wrap;
            margin-top: var(--spacing-lg);
        }
        
        .demo-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            background: var(--gradient-primary);
            color: var(--text-white);
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-weight: 500;
            transition: all var(--transition-fast);
        }
        
        .demo-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }
        
        .demo-btn.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .keyboard-shortcuts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }
        
        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm);
            background: var(--bg-tertiary);
            border-radius: var(--radius-sm);
        }
        
        .shortcut-key {
            background: var(--bg-primary);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-family: monospace;
            font-size: 0.75rem;
            border: 1px solid var(--border-color);
        }
    </style>
</head>
<body>
    <!-- Demo Header -->
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1 class="logo">
                    <i class="fas fa-music"></i>
                    MP3 Audio Player - Demo
                </h1>
                <div class="header-controls">
                    <button class="btn btn-secondary" onclick="window.open('index.html', '_blank')">
                        <i class="fas fa-external-link-alt"></i>
                        Open Full Player
                    </button>
                    <button class="btn btn-secondary" id="themeToggle">
                        <i class="fas fa-palette"></i>
                        Theme
                    </button>
                </div>
            </div>
        </header>

        <!-- Demo Content -->
        <main style="padding: var(--spacing-xl) 0;">
            <!-- Introduction -->
            <div class="demo-section">
                <h2><i class="fas fa-star"></i> Welcome to MP3 Audio Player</h2>
                <p style="font-size: 1.125rem; color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
                    A professional, feature-rich audio player inspired by Sonaar's MP3 Audio Player. 
                    Built with modern web technologies for the best music listening experience.
                </p>
                
                <div class="demo-controls">
                    <button class="demo-btn" onclick="window.open('index.html', '_blank')">
                        <i class="fas fa-play"></i>
                        Launch Player
                    </button>
                    <button class="demo-btn secondary" onclick="document.getElementById('uploadModal').classList.add('active')">
                        <i class="fas fa-upload"></i>
                        Try Upload Demo
                    </button>
                    <button class="demo-btn secondary" onclick="toggleDarkMode()">
                        <i class="fas fa-moon"></i>
                        Toggle Dark Mode
                    </button>
                </div>
            </div>

            <!-- Features -->
            <div class="demo-section">
                <h2><i class="fas fa-rocket"></i> Key Features</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="fas fa-waveform-lines"></i>
                        <h3>Interactive Waveform</h3>
                        <p>Visual waveform display with click-to-seek functionality for precise navigation</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h3>Drag & Drop Upload</h3>
                        <p>Easy file upload with drag-and-drop support for multiple audio formats</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-list-music"></i>
                        <h3>Smart Playlist</h3>
                        <p>Advanced playlist management with search, filter, and sorting capabilities</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-mobile-alt"></i>
                        <h3>Responsive Design</h3>
                        <p>Perfect experience on desktop, tablet, and mobile devices</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-keyboard"></i>
                        <h3>Keyboard Shortcuts</h3>
                        <p>Full keyboard control for power users and accessibility</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-palette"></i>
                        <h3>Dark/Light Themes</h3>
                        <p>Beautiful themes that adapt to your preference and album artwork</p>
                    </div>
                </div>
            </div>

            <!-- Keyboard Shortcuts -->
            <div class="demo-section">
                <h2><i class="fas fa-keyboard"></i> Keyboard Shortcuts</h2>
                <p style="color: var(--text-secondary); margin-bottom: var(--spacing-lg);">
                    Master these shortcuts for the ultimate music experience:
                </p>
                
                <div class="keyboard-shortcuts">
                    <div class="shortcut-item">
                        <span>Play/Pause</span>
                        <span class="shortcut-key">Space</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Seek Forward</span>
                        <span class="shortcut-key">→</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Seek Backward</span>
                        <span class="shortcut-key">←</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Volume Up</span>
                        <span class="shortcut-key">↑</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Volume Down</span>
                        <span class="shortcut-key">↓</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Toggle Mute</span>
                        <span class="shortcut-key">M</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Toggle Shuffle</span>
                        <span class="shortcut-key">S</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Toggle Repeat</span>
                        <span class="shortcut-key">R</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Next Track</span>
                        <span class="shortcut-key">Ctrl+N</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Previous Track</span>
                        <span class="shortcut-key">Ctrl+P</span>
                    </div>
                </div>
            </div>

            <!-- Technical Specs -->
            <div class="demo-section">
                <h2><i class="fas fa-cog"></i> Technical Specifications</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-lg);">
                    <div>
                        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">Supported Formats</h3>
                        <ul style="color: var(--text-secondary); line-height: 1.8;">
                            <li>MP3 (MPEG Audio Layer 3)</li>
                            <li>WAV (Waveform Audio File)</li>
                            <li>OGG (Ogg Vorbis)</li>
                            <li>M4A (MPEG-4 Audio)</li>
                            <li>AAC (Advanced Audio Coding)</li>
                            <li>FLAC (Free Lossless Audio Codec)</li>
                        </ul>
                    </div>
                    <div>
                        <h3 style="color: var(--text-primary); margin-bottom: var(--spacing-md);">Browser Support</h3>
                        <ul style="color: var(--text-secondary); line-height: 1.8;">
                            <li>Chrome 60+ ✅</li>
                            <li>Firefox 55+ ✅</li>
                            <li>Safari 11+ ✅</li>
                            <li>Edge 79+ ✅</li>
                            <li>Mobile browsers ✅</li>
                            <li>Progressive Web App ready 🚀</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="demo-section" style="text-align: center;">
                <h2><i class="fas fa-headphones"></i> Ready to Experience Amazing Audio?</h2>
                <p style="font-size: 1.125rem; color: var(--text-secondary); margin-bottom: var(--spacing-xl);">
                    Upload your favorite tracks and enjoy professional-grade audio playback
                </p>
                
                <div class="demo-controls" style="justify-content: center;">
                    <button class="demo-btn" onclick="window.open('index.html', '_blank')" style="font-size: 1.125rem; padding: var(--spacing-md) var(--spacing-xl);">
                        <i class="fas fa-play"></i>
                        Launch MP3 Audio Player
                    </button>
                </div>
            </div>
        </main>
    </div>

    <!-- Simple Upload Modal for Demo -->
    <div class="modal" id="uploadModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Upload Demo</h3>
                <button class="modal-close" onclick="document.getElementById('uploadModal').classList.remove('active')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="upload-area">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>This is a demo of the upload interface</p>
                    <p style="font-size: 0.875rem; color: var(--text-muted);">
                        In the full player, you can drag & drop MP3 files here or click to browse
                    </p>
                    <button class="btn btn-primary" onclick="window.open('index.html', '_blank')">
                        Try Real Upload
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple theme toggle for demo
        function toggleDarkMode() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('demo_theme', newTheme);
        }

        // Load saved theme
        const savedTheme = localStorage.getItem('demo_theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);

        // Theme toggle button
        document.getElementById('themeToggle').addEventListener('click', toggleDarkMode);

        // Close modal on escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                document.getElementById('uploadModal').classList.remove('active');
            }
        });

        console.log('🎵 MP3 Audio Player Demo loaded');
        console.log('👉 Click "Launch Player" to try the full application');
    </script>
</body>
</html>
