<?php
/**
 * Helper Functions
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get plugin settings
 */
function mp3_audio_player_pro_get_settings() {
    $defaults = array(
        'default_theme' => 'light',
        'autoplay' => false,
        'default_volume' => 1,
        'show_waveform' => true,
        'sticky_player' => true,
        'load_everywhere' => false,
        'enable_analytics' => false,
        'custom_css' => '',
    );
    
    $settings = get_option('mp3_audio_player_pro_settings', array());
    return wp_parse_args($settings, $defaults);
}

/**
 * Get a specific setting
 */
function mp3_audio_player_pro_get_setting($key, $default = null) {
    $settings = mp3_audio_player_pro_get_settings();
    return isset($settings[$key]) ? $settings[$key] : $default;
}

/**
 * Format duration in seconds to MM:SS format
 */
function mp3_audio_player_pro_format_duration($seconds) {
    if (!$seconds || !is_numeric($seconds)) {
        return '0:00';
    }
    
    $minutes = floor($seconds / 60);
    $remaining_seconds = $seconds % 60;
    
    return sprintf('%d:%02d', $minutes, $remaining_seconds);
}

/**
 * Format file size
 */
function mp3_audio_player_pro_format_file_size($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

/**
 * Get default album artwork URL
 */
function mp3_audio_player_pro_get_default_artwork() {
    return MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/images/default-album.jpg';
}

/**
 * Check if user can manage audio player
 */
function mp3_audio_player_pro_user_can_manage() {
    return current_user_can('edit_posts');
}

/**
 * Get supported audio formats
 */
function mp3_audio_player_pro_get_supported_formats() {
    return apply_filters('mp3_audio_player_pro_supported_formats', array(
        'mp3' => array(
            'mime' => 'audio/mpeg',
            'name' => 'MP3',
        ),
        'wav' => array(
            'mime' => 'audio/wav',
            'name' => 'WAV',
        ),
        'ogg' => array(
            'mime' => 'audio/ogg',
            'name' => 'OGG',
        ),
        'm4a' => array(
            'mime' => 'audio/mp4',
            'name' => 'M4A',
        ),
        'aac' => array(
            'mime' => 'audio/aac',
            'name' => 'AAC',
        ),
        'flac' => array(
            'mime' => 'audio/flac',
            'name' => 'FLAC',
        ),
    ));
}

/**
 * Sanitize color value
 */
function mp3_audio_player_pro_sanitize_color($color) {
    if (empty($color)) {
        return '';
    }
    
    // Remove # if present
    $color = ltrim($color, '#');
    
    // Check if valid hex color
    if (preg_match('/^[a-fA-F0-9]{6}$/', $color)) {
        return '#' . $color;
    }
    
    return '';
}

/**
 * Get player layouts
 */
function mp3_audio_player_pro_get_layouts() {
    return apply_filters('mp3_audio_player_pro_layouts', array(
        'default' => __('Default', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
        'compact' => __('Compact', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
        'mini' => __('Mini', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
    ));
}

/**
 * Get player themes
 */
function mp3_audio_player_pro_get_themes() {
    return apply_filters('mp3_audio_player_pro_themes', array(
        'light' => __('Light', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
        'dark' => __('Dark', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
    ));
}

/**
 * Enqueue player assets for a specific player
 */
function mp3_audio_player_pro_enqueue_assets() {
    static $assets_enqueued = false;
    
    if ($assets_enqueued) {
        return;
    }
    
    // CSS
    wp_enqueue_style(
        'mp3-audio-player-pro-style',
        MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/css/style.css',
        array(),
        MP3_AUDIO_PLAYER_PRO_VERSION
    );
    
    // Google Fonts
    wp_enqueue_style(
        'mp3-audio-player-pro-fonts',
        'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
        array(),
        null
    );
    
    // Font Awesome
    wp_enqueue_style(
        'mp3-audio-player-pro-fontawesome',
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
        array(),
        '6.4.0'
    );
    
    // JavaScript
    wp_enqueue_script(
        'mp3-audio-player-pro-audio-engine',
        MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/audio-engine.js',
        array(),
        MP3_AUDIO_PLAYER_PRO_VERSION,
        true
    );
    
    wp_enqueue_script(
        'mp3-audio-player-pro-playlist-manager',
        MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/playlist-manager.js',
        array('mp3-audio-player-pro-audio-engine'),
        MP3_AUDIO_PLAYER_PRO_VERSION,
        true
    );
    
    wp_enqueue_script(
        'mp3-audio-player-pro-waveform',
        MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/waveform.js',
        array('mp3-audio-player-pro-audio-engine'),
        MP3_AUDIO_PLAYER_PRO_VERSION,
        true
    );
    
    wp_enqueue_script(
        'mp3-audio-player-pro-ui-controller',
        MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/ui-controller.js',
        array('mp3-audio-player-pro-audio-engine', 'mp3-audio-player-pro-playlist-manager'),
        MP3_AUDIO_PLAYER_PRO_VERSION,
        true
    );
    
    wp_enqueue_script(
        'mp3-audio-player-pro-file-manager',
        MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/file-manager.js',
        array('mp3-audio-player-pro-playlist-manager'),
        MP3_AUDIO_PLAYER_PRO_VERSION,
        true
    );
    
    wp_enqueue_script(
        'mp3-audio-player-pro-app',
        MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/app.js',
        array(
            'mp3-audio-player-pro-audio-engine',
            'mp3-audio-player-pro-playlist-manager',
            'mp3-audio-player-pro-waveform',
            'mp3-audio-player-pro-ui-controller',
            'mp3-audio-player-pro-file-manager'
        ),
        MP3_AUDIO_PLAYER_PRO_VERSION,
        true
    );
    
    // Localize script
    wp_localize_script(
        'mp3-audio-player-pro-app',
        'mp3PlayerPro',
        array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('mp3_player_frontend_nonce'),
            'pluginUrl' => MP3_AUDIO_PLAYER_PRO_PLUGIN_URL,
            'settings' => mp3_audio_player_pro_get_settings(),
            'strings' => array(
                'selectTrack' => __('Select a track to play', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'unknownArtist' => __('Unknown Artist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'unknownAlbum' => __('Unknown Album', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'noTracks' => __('No tracks in playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'uploadMusic' => __('Upload some music to get started', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'searchTracks' => __('Search tracks...', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'sortByTitle' => __('Sort by Title', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'sortByArtist' => __('Sort by Artist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'sortByAlbum' => __('Sort by Album', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'sortByDuration' => __('Sort by Duration', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'playbackSpeed' => __('Playback Speed', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'download' => __('Download', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'share' => __('Share', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'clear' => __('Clear', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'save' => __('Save', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'playlist' => __('Playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'confirmDelete' => __('Are you sure you want to delete this item?', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'uploadError' => __('Upload failed. Please try again.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'saveSuccess' => __('Saved successfully!', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            ),
        )
    );
    
    $assets_enqueued = true;
}

/**
 * Add custom CSS to head
 */
function mp3_audio_player_pro_add_custom_css() {
    $custom_css = mp3_audio_player_pro_get_setting('custom_css');
    
    if (!empty($custom_css)) {
        echo '<style type="text/css" id="mp3-audio-player-pro-custom-css">' . wp_strip_all_tags($custom_css) . '</style>';
    }
}
add_action('wp_head', 'mp3_audio_player_pro_add_custom_css');

/**
 * Get upload directory for audio files
 */
function mp3_audio_player_pro_get_upload_dir() {
    $upload_dir = wp_upload_dir();
    $plugin_upload_dir = $upload_dir['basedir'] . '/mp3-audio-player-pro';
    $plugin_upload_url = $upload_dir['baseurl'] . '/mp3-audio-player-pro';
    
    // Create directory if it doesn't exist
    if (!file_exists($plugin_upload_dir)) {
        wp_mkdir_p($plugin_upload_dir);
        
        // Add .htaccess for security
        $htaccess_content = "Options -Indexes\n";
        $htaccess_content .= "<Files *.php>\n";
        $htaccess_content .= "deny from all\n";
        $htaccess_content .= "</Files>\n";
        
        file_put_contents($plugin_upload_dir . '/.htaccess', $htaccess_content);
    }
    
    return array(
        'path' => $plugin_upload_dir,
        'url' => $plugin_upload_url,
    );
}

/**
 * Log player events for analytics
 */
function mp3_audio_player_pro_log_event($event_type, $track_id = null, $user_id = null) {
    if (!mp3_audio_player_pro_get_setting('enable_analytics')) {
        return;
    }
    
    $user_id = $user_id ?: get_current_user_id();
    
    // Log to WordPress database or external service
    do_action('mp3_audio_player_pro_log_event', $event_type, $track_id, $user_id);
}

/**
 * Get player shortcode with attributes
 */
function mp3_audio_player_pro_get_shortcode($atts = array()) {
    $default_atts = array(
        'playlist' => '',
        'tracks' => '',
        'theme' => mp3_audio_player_pro_get_setting('default_theme'),
        'layout' => 'default',
        'show_waveform' => mp3_audio_player_pro_get_setting('show_waveform') ? 'true' : 'false',
        'show_playlist' => 'true',
        'sticky_player' => mp3_audio_player_pro_get_setting('sticky_player') ? 'true' : 'false',
        'autoplay' => mp3_audio_player_pro_get_setting('autoplay') ? 'true' : 'false',
        'volume' => mp3_audio_player_pro_get_setting('default_volume'),
    );
    
    $atts = wp_parse_args($atts, $default_atts);
    
    $shortcode_atts = array();
    foreach ($atts as $key => $value) {
        if (!empty($value)) {
            $shortcode_atts[] = $key . '="' . esc_attr($value) . '"';
        }
    }
    
    return '[mp3_audio_player ' . implode(' ', $shortcode_atts) . ']';
}

/**
 * Check if current page should load player assets
 */
function mp3_audio_player_pro_should_load_assets() {
    global $post;
    
    // Always load if setting is enabled
    if (mp3_audio_player_pro_get_setting('load_everywhere')) {
        return true;
    }
    
    // Load on pages with shortcode
    if ($post && has_shortcode($post->post_content, 'mp3_audio_player')) {
        return true;
    }
    
    // Load if widget is active
    if (is_active_widget(false, false, 'mp3_audio_player_widget')) {
        return true;
    }
    
    // Load on plugin admin pages
    if (is_admin() && isset($_GET['page']) && strpos($_GET['page'], 'mp3-audio-player') !== false) {
        return true;
    }
    
    return false;
}

/**
 * Get player instance count
 */
function mp3_audio_player_pro_get_instance_count() {
    static $instance_count = 0;
    return ++$instance_count;
}

/**
 * Validate shortcode attributes
 */
function mp3_audio_player_pro_validate_shortcode_atts($atts) {
    $valid_layouts = array_keys(mp3_audio_player_pro_get_layouts());
    $valid_themes = array_keys(mp3_audio_player_pro_get_themes());
    
    // Validate layout
    if (isset($atts['layout']) && !in_array($atts['layout'], $valid_layouts)) {
        $atts['layout'] = 'default';
    }
    
    // Validate theme
    if (isset($atts['theme']) && !in_array($atts['theme'], $valid_themes)) {
        $atts['theme'] = 'light';
    }
    
    // Validate boolean values
    $boolean_atts = array('show_waveform', 'show_playlist', 'sticky_player', 'autoplay');
    foreach ($boolean_atts as $att) {
        if (isset($atts[$att])) {
            $atts[$att] = filter_var($atts[$att], FILTER_VALIDATE_BOOLEAN) ? 'true' : 'false';
        }
    }
    
    // Validate volume
    if (isset($atts['volume'])) {
        $atts['volume'] = max(0, min(1, floatval($atts['volume'])));
    }
    
    return $atts;
}
