<?php
/**
 * Playlists Admin Page
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$db = MP3_Audio_Player_Pro_Database::get_instance();
$playlists = $db->get_playlists(get_current_user_id());
?>

<div class="wrap">
    <h1>
        <?php _e('Playlists', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
        <a href="#" class="page-title-action" onclick="showCreatePlaylistModal()">
            <?php _e('Add New', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
        </a>
    </h1>
    
    <?php if (!empty($playlists)): ?>
        <div class="playlists-grid">
            <?php foreach ($playlists as $playlist): ?>
                <?php 
                $tracks = $db->get_tracks(null, $playlist->id);
                $total_duration = array_sum(array_column($tracks, 'duration'));
                ?>
                <div class="playlist-card" data-playlist-id="<?php echo $playlist->id; ?>">
                    <div class="playlist-header">
                        <h3><?php echo esc_html($playlist->name); ?></h3>
                        <div class="playlist-actions">
                            <button class="button button-small" onclick="editPlaylist(<?php echo $playlist->id; ?>)">
                                <?php _e('Edit', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </button>
                            <button class="button button-small button-link-delete" onclick="deletePlaylist(<?php echo $playlist->id; ?>)">
                                <?php _e('Delete', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </button>
                        </div>
                    </div>
                    
                    <div class="playlist-meta">
                        <p class="playlist-description"><?php echo esc_html($playlist->description); ?></p>
                        <div class="playlist-stats">
                            <span class="track-count">
                                <?php printf(_n('%d track', '%d tracks', count($tracks), MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), count($tracks)); ?>
                            </span>
                            <span class="total-duration">
                                <?php echo mp3_audio_player_pro_format_duration($total_duration); ?>
                            </span>
                            <?php if ($playlist->is_public): ?>
                                <span class="public-badge"><?php _e('Public', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="playlist-tracks">
                        <?php if (!empty($tracks)): ?>
                            <ul class="track-list">
                                <?php foreach (array_slice($tracks, 0, 5) as $track): ?>
                                    <li class="track-item">
                                        <span class="track-title"><?php echo esc_html($track->title); ?></span>
                                        <span class="track-artist"><?php echo esc_html($track->artist); ?></span>
                                        <span class="track-duration"><?php echo mp3_audio_player_pro_format_duration($track->duration); ?></span>
                                    </li>
                                <?php endforeach; ?>
                                <?php if (count($tracks) > 5): ?>
                                    <li class="track-item more-tracks">
                                        <?php printf(__('+ %d more tracks', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN), count($tracks) - 5); ?>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        <?php else: ?>
                            <p class="no-tracks"><?php _e('No tracks in this playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        <?php endif; ?>
                    </div>
                    
                    <div class="playlist-footer">
                        <div class="shortcode-preview">
                            <code>[mp3_audio_player playlist="<?php echo $playlist->id; ?>"]</code>
                            <button class="copy-shortcode" onclick="copyShortcode(this)" data-shortcode='[mp3_audio_player playlist="<?php echo $playlist->id; ?>"]'>
                                <?php _e('Copy', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="empty-state">
            <i class="dashicons dashicons-playlist-audio"></i>
            <h3><?php _e('No playlists found', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h3>
            <p><?php _e('Create your first playlist to organize your audio tracks.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
            <button class="button button-primary" onclick="showCreatePlaylistModal()">
                <?php _e('Create Playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
            </button>
        </div>
    <?php endif; ?>
</div>

<!-- Create/Edit Playlist Modal -->
<div id="playlist-modal" class="playlist-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modal-title"><?php _e('Create Playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h2>
            <button class="modal-close" onclick="hidePlaylistModal()">&times;</button>
        </div>
        
        <form id="playlist-form">
            <div class="modal-body">
                <input type="hidden" id="playlist-id" value="">
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Name', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <input type="text" id="playlist-name" class="regular-text" required>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Description', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <textarea id="playlist-description" class="large-text" rows="3"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Visibility', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" id="playlist-public">
                                <?php _e('Make this playlist public', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                            </label>
                        </td>
                    </tr>
                </table>
                
                <div class="track-selection">
                    <h3><?php _e('Select Tracks', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></h3>
                    <div class="available-tracks">
                        <?php 
                        $all_tracks = $db->get_tracks(get_current_user_id());
                        if (!empty($all_tracks)):
                        ?>
                            <div class="tracks-search">
                                <input type="text" id="tracks-search" placeholder="<?php _e('Search tracks...', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>">
                            </div>
                            <div class="tracks-list" id="available-tracks-list">
                                <?php foreach ($all_tracks as $track): ?>
                                    <div class="track-option" data-track-id="<?php echo $track->id; ?>">
                                        <label>
                                            <input type="checkbox" name="playlist_tracks[]" value="<?php echo $track->id; ?>">
                                            <span class="track-info">
                                                <strong><?php echo esc_html($track->title); ?></strong>
                                                <span class="track-meta"><?php echo esc_html($track->artist); ?> - <?php echo esc_html($track->album); ?></span>
                                            </span>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <p><?php _e('No tracks available. Upload some audio files first.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="button" onclick="hidePlaylistModal()">
                    <?php _e('Cancel', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                </button>
                <button type="submit" class="button button-primary">
                    <?php _e('Save Playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.playlists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.playlist-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.playlist-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.playlist-header h3 {
    margin: 0;
    font-size: 18px;
}

.playlist-actions {
    display: flex;
    gap: 5px;
}

.playlist-meta {
    margin-bottom: 15px;
}

.playlist-description {
    color: #666;
    margin-bottom: 10px;
}

.playlist-stats {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #666;
}

.public-badge {
    background: #00a32a;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
}

.track-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.track-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
}

.track-item:last-child {
    border-bottom: none;
}

.track-title {
    font-weight: 500;
    flex: 1;
}

.track-artist {
    color: #666;
    margin: 0 10px;
}

.track-duration {
    color: #999;
}

.more-tracks {
    color: #666;
    font-style: italic;
}

.no-tracks {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 20px 0;
}

.playlist-footer {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.shortcode-preview {
    display: flex;
    align-items: center;
    gap: 10px;
}

.shortcode-preview code {
    flex: 1;
    background: #f9f9f9;
    padding: 5px 8px;
    border-radius: 3px;
    font-size: 11px;
}

.copy-shortcode {
    font-size: 11px;
    padding: 3px 8px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state .dashicons {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.3;
}

/* Modal Styles */
.playlist-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
}

.modal-header h2 {
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    text-align: right;
}

.track-selection {
    margin-top: 20px;
}

.tracks-search {
    margin-bottom: 10px;
}

.tracks-search input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.tracks-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}

.track-option {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.track-option:last-child {
    border-bottom: none;
}

.track-option label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.track-option input {
    margin-right: 10px;
}

.track-info {
    display: flex;
    flex-direction: column;
}

.track-meta {
    font-size: 12px;
    color: #666;
}
</style>

<script>
function showCreatePlaylistModal() {
    document.getElementById('modal-title').textContent = '<?php _e('Create Playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>';
    document.getElementById('playlist-form').reset();
    document.getElementById('playlist-id').value = '';
    document.getElementById('playlist-modal').style.display = 'flex';
}

function hidePlaylistModal() {
    document.getElementById('playlist-modal').style.display = 'none';
}

function editPlaylist(playlistId) {
    // Load playlist data via AJAX
    var data = {
        action: 'mp3_player_get_playlist',
        playlist_id: playlistId,
        nonce: '<?php echo wp_create_nonce('mp3_player_admin_nonce'); ?>'
    };
    
    jQuery.post(ajaxurl, data, function(response) {
        if (response.success) {
            var playlist = response.data.playlist;
            var tracks = response.data.tracks;
            
            document.getElementById('modal-title').textContent = '<?php _e('Edit Playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>';
            document.getElementById('playlist-id').value = playlist.id;
            document.getElementById('playlist-name').value = playlist.name;
            document.getElementById('playlist-description').value = playlist.description;
            document.getElementById('playlist-public').checked = playlist.is_public;
            
            // Check tracks that are in this playlist
            var trackIds = tracks.map(function(track) { return track.id.toString(); });
            var checkboxes = document.querySelectorAll('input[name="playlist_tracks[]"]');
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = trackIds.includes(checkbox.value);
            });
            
            document.getElementById('playlist-modal').style.display = 'flex';
        }
    });
}

function deletePlaylist(playlistId) {
    if (!confirm('<?php _e('Are you sure you want to delete this playlist?', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>')) {
        return;
    }
    
    var data = {
        action: 'mp3_player_delete_playlist',
        playlist_id: playlistId,
        nonce: '<?php echo wp_create_nonce('mp3_player_admin_nonce'); ?>'
    };
    
    jQuery.post(ajaxurl, data, function(response) {
        if (response.success) {
            location.reload();
        } else {
            alert(response.data || '<?php _e('Failed to delete playlist.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>');
        }
    });
}

function copyShortcode(button) {
    var shortcode = button.getAttribute('data-shortcode');
    navigator.clipboard.writeText(shortcode).then(function() {
        var originalText = button.textContent;
        button.textContent = '<?php _e('Copied!', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>';
        setTimeout(function() {
            button.textContent = originalText;
        }, 2000);
    });
}

// Handle playlist form submission
document.getElementById('playlist-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    var playlistId = document.getElementById('playlist-id').value;
    var formData = new FormData();
    
    formData.append('action', playlistId ? 'mp3_player_update_playlist' : 'mp3_player_create_playlist');
    formData.append('nonce', '<?php echo wp_create_nonce('mp3_player_admin_nonce'); ?>');
    
    if (playlistId) {
        formData.append('playlist_id', playlistId);
    }
    
    formData.append('name', document.getElementById('playlist-name').value);
    formData.append('description', document.getElementById('playlist-description').value);
    formData.append('is_public', document.getElementById('playlist-public').checked ? '1' : '0');
    
    // Add selected tracks
    var selectedTracks = [];
    document.querySelectorAll('input[name="playlist_tracks[]"]:checked').forEach(function(checkbox) {
        selectedTracks.push(checkbox.value);
    });
    formData.append('tracks', JSON.stringify(selectedTracks));
    
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert(response.data || '<?php _e('Failed to save playlist.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>');
            }
        }
    });
});

// Track search functionality
document.getElementById('tracks-search').addEventListener('input', function(e) {
    var searchTerm = e.target.value.toLowerCase();
    var trackOptions = document.querySelectorAll('.track-option');
    
    trackOptions.forEach(function(option) {
        var trackInfo = option.querySelector('.track-info').textContent.toLowerCase();
        if (trackInfo.includes(searchTerm)) {
            option.style.display = 'block';
        } else {
            option.style.display = 'none';
        }
    });
});

// Close modal when clicking outside
document.getElementById('playlist-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        hidePlaylistModal();
    }
});
</script>
