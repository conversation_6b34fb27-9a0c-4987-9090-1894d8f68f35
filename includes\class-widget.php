<?php
/**
 * Widget Class
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * MP3 Audio Player Pro Widget
 */
class MP3_Audio_Player_Pro_Widget extends WP_Widget {
    
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct(
            'mp3_audio_player_widget',
            __('MP3 Audio Player', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array(
                'description' => __('Add an MP3 audio player to your sidebar.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                'classname' => 'mp3-audio-player-widget',
            )
        );
    }
    
    /**
     * Widget output
     */
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        
        // Build shortcode attributes
        $shortcode_atts = array();
        
        if (!empty($instance['playlist'])) {
            $shortcode_atts[] = 'playlist="' . esc_attr($instance['playlist']) . '"';
        }
        
        if (!empty($instance['layout'])) {
            $shortcode_atts[] = 'layout="' . esc_attr($instance['layout']) . '"';
        }
        
        if (!empty($instance['theme'])) {
            $shortcode_atts[] = 'theme="' . esc_attr($instance['theme']) . '"';
        }
        
        if (isset($instance['show_playlist']) && !$instance['show_playlist']) {
            $shortcode_atts[] = 'show_playlist="false"';
        }
        
        if (isset($instance['show_waveform']) && !$instance['show_waveform']) {
            $shortcode_atts[] = 'show_waveform="false"';
        }
        
        $shortcode = '[mp3_audio_player ' . implode(' ', $shortcode_atts) . ']';
        echo do_shortcode($shortcode);
        
        echo $args['after_widget'];
    }
    
    /**
     * Widget form
     */
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : '';
        $playlist = !empty($instance['playlist']) ? $instance['playlist'] : '';
        $layout = !empty($instance['layout']) ? $instance['layout'] : 'compact';
        $theme = !empty($instance['theme']) ? $instance['theme'] : 'light';
        $show_playlist = isset($instance['show_playlist']) ? (bool) $instance['show_playlist'] : false;
        $show_waveform = isset($instance['show_waveform']) ? (bool) $instance['show_waveform'] : true;
        
        // Get playlists
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        $playlists = $db->get_playlists();
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>">
                <?php _e('Title:', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
            </label>
            <input class="widefat" 
                   id="<?php echo esc_attr($this->get_field_id('title')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('title')); ?>" 
                   type="text" 
                   value="<?php echo esc_attr($title); ?>">
        </p>
        
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('playlist')); ?>">
                <?php _e('Playlist:', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
            </label>
            <select class="widefat" 
                    id="<?php echo esc_attr($this->get_field_id('playlist')); ?>" 
                    name="<?php echo esc_attr($this->get_field_name('playlist')); ?>">
                <option value=""><?php _e('All Tracks', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                <?php foreach ($playlists as $pl): ?>
                    <option value="<?php echo esc_attr($pl->id); ?>" <?php selected($playlist, $pl->id); ?>>
                        <?php echo esc_html($pl->name); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </p>
        
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('layout')); ?>">
                <?php _e('Layout:', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
            </label>
            <select class="widefat" 
                    id="<?php echo esc_attr($this->get_field_id('layout')); ?>" 
                    name="<?php echo esc_attr($this->get_field_name('layout')); ?>">
                <option value="mini" <?php selected($layout, 'mini'); ?>><?php _e('Mini', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                <option value="compact" <?php selected($layout, 'compact'); ?>><?php _e('Compact', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                <option value="default" <?php selected($layout, 'default'); ?>><?php _e('Default', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
            </select>
        </p>
        
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('theme')); ?>">
                <?php _e('Theme:', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
            </label>
            <select class="widefat" 
                    id="<?php echo esc_attr($this->get_field_id('theme')); ?>" 
                    name="<?php echo esc_attr($this->get_field_name('theme')); ?>">
                <option value="light" <?php selected($theme, 'light'); ?>><?php _e('Light', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                <option value="dark" <?php selected($theme, 'dark'); ?>><?php _e('Dark', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
            </select>
        </p>
        
        <p>
            <input class="checkbox" 
                   type="checkbox" 
                   <?php checked($show_playlist); ?> 
                   id="<?php echo esc_attr($this->get_field_id('show_playlist')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('show_playlist')); ?>" 
                   value="1">
            <label for="<?php echo esc_attr($this->get_field_id('show_playlist')); ?>">
                <?php _e('Show Playlist', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
            </label>
        </p>
        
        <p>
            <input class="checkbox" 
                   type="checkbox" 
                   <?php checked($show_waveform); ?> 
                   id="<?php echo esc_attr($this->get_field_id('show_waveform')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('show_waveform')); ?>" 
                   value="1">
            <label for="<?php echo esc_attr($this->get_field_id('show_waveform')); ?>">
                <?php _e('Show Waveform', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
            </label>
        </p>
        <?php
    }
    
    /**
     * Update widget
     */
    public function update($new_instance, $old_instance) {
        $instance = array();
        
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['playlist'] = (!empty($new_instance['playlist'])) ? intval($new_instance['playlist']) : '';
        $instance['layout'] = (!empty($new_instance['layout'])) ? sanitize_text_field($new_instance['layout']) : 'compact';
        $instance['theme'] = (!empty($new_instance['theme'])) ? sanitize_text_field($new_instance['theme']) : 'light';
        $instance['show_playlist'] = !empty($new_instance['show_playlist']);
        $instance['show_waveform'] = !empty($new_instance['show_waveform']);
        
        return $instance;
    }
}
