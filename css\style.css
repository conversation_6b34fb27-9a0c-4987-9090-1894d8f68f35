/* CSS Variables for Theme Management */
:root {
    /* Primary Colors */
    --primary-color: #6366f1;
    --primary-hover: #5855eb;
    --primary-light: #a5b4fc;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #0f172a;
    --bg-card: #ffffff;
    
    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-white: #ffffff;
    
    /* Border Colors */
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    
    /* Shadow */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #334155;
    --border-hover: #475569;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    min-height: 100vh;
}

/* Header */
.header {
    padding: var(--spacing-lg) 0;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-card);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.header-controls {
    display: flex;
    gap: var(--spacing-sm);
}

/* Buttons */
.btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    text-decoration: none;
    font-size: 0.875rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-white);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-secondary);
    border-color: var(--border-hover);
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.75rem;
}

/* Main Content */
.main-content {
    padding: var(--spacing-xl) 0;
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--spacing-2xl);
}

/* Player Section */
.player-section {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.player-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Album Art */
.album-art-container {
    display: flex;
    justify-content: center;
}

.album-art {
    position: relative;
    width: 300px;
    height: 300px;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.album-art img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.album-art-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.album-art:hover .album-art-overlay {
    opacity: 1;
}

.album-art:hover img {
    transform: scale(1.05);
}

.play-btn-large {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    border: none;
    color: var(--text-white);
    font-size: 2rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-btn-large:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

/* Track Info */
.track-info {
    text-align: center;
}

.track-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.track-artist {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.track-album {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Waveform */
.waveform-container {
    position: relative;
    height: 80px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    overflow: hidden;
    cursor: pointer;
}

.waveform-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

.waveform-progress {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0.7;
    width: 0%;
    transition: width var(--transition-fast);
    pointer-events: none;
}

/* Player Controls */
.player-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.control-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);
}

.control-btn {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-full);
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.control-btn:hover {
    background: var(--bg-secondary);
    transform: translateY(-1px);
}

.play-pause-btn {
    width: 64px;
    height: 64px;
    background: var(--gradient-primary);
    color: var(--text-white);
    font-size: 1.5rem;
}

.play-pause-btn:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

/* Progress Container */
.progress-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.time-display {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 40px;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    position: relative;
    cursor: pointer;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    width: 0%;
    transition: width var(--transition-fast);
}

.progress-handle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: var(--radius-full);
    cursor: pointer;
    opacity: 0;
    transition: opacity var(--transition-fast);
    left: 0%;
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

/* Volume Container */
.volume-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.volume-bar {
    width: 100px;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    position: relative;
    cursor: pointer;
}

.volume-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    width: 100%;
    transition: width var(--transition-fast);
}

.volume-handle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: var(--radius-full);
    cursor: pointer;
    opacity: 0;
    transition: opacity var(--transition-fast);
    right: 0%;
}

.volume-bar:hover .volume-handle {
    opacity: 1;
}

/* Additional Controls */
.additional-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
}

.speed-text {
    font-size: 0.75rem;
    font-weight: 600;
}

/* Playlist Section */
.playlist-section {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    height: fit-content;
}

.playlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.playlist-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.playlist-controls {
    display: flex;
    gap: var(--spacing-xs);
}

/* Search and Filter */
.search-filter-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: var(--spacing-md);
    color: var(--text-muted);
    z-index: 1;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-controls select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
}

/* Playlist Container */
.playlist-container {
    max-height: 500px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--bg-tertiary);
}

.playlist-container::-webkit-scrollbar {
    width: 6px;
}

.playlist-container::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
}

.playlist-container::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: var(--radius-full);
}

.playlist-empty {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-muted);
}

.playlist-empty i {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.playlist-empty .subtitle {
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
}

/* Playlist Item */
.playlist-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    border: 1px solid transparent;
    margin-bottom: var(--spacing-xs);
}

.playlist-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

.playlist-item.active {
    background: var(--primary-light);
    border-color: var(--primary-color);
}

.playlist-item.playing {
    background: var(--gradient-primary);
    color: var(--text-white);
}

.playlist-item-number {
    width: 24px;
    text-align: center;
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-right: var(--spacing-md);
}

.playlist-item.playing .playlist-item-number {
    color: var(--text-white);
}

.playlist-item-play {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    border: none;
    background: var(--primary-color);
    color: var(--text-white);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    opacity: 0;
    transition: all var(--transition-fast);
}

.playlist-item:hover .playlist-item-play {
    opacity: 1;
}

.playlist-item-info {
    flex: 1;
    min-width: 0;
}

.playlist-item-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.playlist-item.playing .playlist-item-title {
    color: var(--text-white);
}

.playlist-item-meta {
    font-size: 0.75rem;
    color: var(--text-muted);
    display: flex;
    gap: var(--spacing-sm);
}

.playlist-item.playing .playlist-item-meta {
    color: rgba(255, 255, 255, 0.8);
}

.playlist-item-duration {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-left: var(--spacing-md);
}

.playlist-item.playing .playlist-item-duration {
    color: var(--text-white);
}

.playlist-item-actions {
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.playlist-item:hover .playlist-item-actions {
    opacity: 1;
}

.playlist-item-action {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.playlist-item-action:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .album-art {
        width: 250px;
        height: 250px;
    }

    .player-section {
        padding: var(--spacing-lg);
    }

    .control-buttons {
        gap: var(--spacing-sm);
    }

    .control-btn {
        width: 40px;
        height: 40px;
    }

    .play-pause-btn {
        width: 56px;
        height: 56px;
    }

    .playlist-section {
        padding: var(--spacing-lg);
    }

    .search-filter-container {
        flex-direction: column;
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    color: var(--text-muted);
    cursor: pointer;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all var(--transition-fast);
    cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.upload-area i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.upload-area p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.upload-area input[type="file"] {
    display: none;
}

.upload-progress {
    margin-top: var(--spacing-lg);
}

.upload-progress .progress-bar {
    height: 8px;
    margin-bottom: var(--spacing-sm);
}

#uploadStatus {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Sticky Player */
.sticky-player {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bg-card);
    border-top: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    z-index: 999;
    transform: translateY(100%);
    transition: transform var(--transition-normal);
    backdrop-filter: blur(10px);
}

.sticky-player.active {
    transform: translateY(0);
}

.sticky-player-content {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    gap: var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.sticky-track-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    min-width: 200px;
}

.sticky-album-art {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
    object-fit: cover;
}

.sticky-text-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.sticky-title {
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.875rem;
}

.sticky-artist {
    font-size: 0.75rem;
    color: var(--text-muted);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sticky-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sticky-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: none;
    color: var(--text-primary);
    cursor: pointer;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.sticky-btn:hover {
    background: var(--bg-tertiary);
}

.sticky-play-btn {
    background: var(--gradient-primary);
    color: var(--text-white);
}

.sticky-play-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.sticky-progress {
    flex: 1;
    margin: 0 var(--spacing-lg);
}

.sticky-progress-bar {
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    cursor: pointer;
    position: relative;
}

.sticky-progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    width: 0%;
    transition: width var(--transition-fast);
}

.sticky-volume {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 120px;
}

.sticky-volume-bar {
    width: 80px;
    height: 3px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    cursor: pointer;
    position: relative;
}

.sticky-volume-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    width: 100%;
    transition: width var(--transition-fast);
}

/* Mobile Sticky Player */
@media (max-width: 768px) {
    .sticky-player-content {
        padding: var(--spacing-sm) var(--spacing-md);
        gap: var(--spacing-md);
    }

    .sticky-track-info {
        min-width: 150px;
    }

    .sticky-album-art {
        width: 40px;
        height: 40px;
    }

    .sticky-btn {
        width: 32px;
        height: 32px;
    }

    .sticky-volume {
        display: none;
    }

    .sticky-progress {
        margin: 0 var(--spacing-sm);
    }
}
