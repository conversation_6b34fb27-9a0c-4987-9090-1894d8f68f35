<?php
/**
 * Admin Interface Class
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * MP3 Audio Player Pro Admin Class
 */
class MP3_Audio_Player_Pro_Admin {
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('admin_init', array($this, 'init'));
        add_action('admin_notices', array($this, 'admin_notices'));
    }
    
    /**
     * Initialize admin
     */
    public function init() {
        // Register settings
        $this->register_settings();
        
        // Add meta boxes for posts/pages
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_post_meta'));
        
        // Add media button
        add_action('media_buttons', array($this, 'add_media_button'));
        
        // Add TinyMCE plugin
        add_filter('mce_external_plugins', array($this, 'add_tinymce_plugin'));
        add_filter('mce_buttons', array($this, 'add_tinymce_button'));
    }
    
    /**
     * Register plugin settings
     */
    private function register_settings() {
        // General settings
        register_setting(
            'mp3_audio_player_pro_settings',
            'mp3_audio_player_pro_settings',
            array($this, 'sanitize_settings')
        );
        
        // Settings sections
        add_settings_section(
            'mp3_player_general',
            __('General Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'general_section_callback'),
            'mp3_audio_player_pro_settings'
        );
        
        add_settings_section(
            'mp3_player_appearance',
            __('Appearance Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'appearance_section_callback'),
            'mp3_audio_player_pro_settings'
        );
        
        add_settings_section(
            'mp3_player_advanced',
            __('Advanced Settings', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'advanced_section_callback'),
            'mp3_audio_player_pro_settings'
        );
        
        // Settings fields
        $this->add_settings_fields();
    }
    
    /**
     * Add settings fields
     */
    private function add_settings_fields() {
        // General settings
        add_settings_field(
            'default_theme',
            __('Default Theme', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'theme_field_callback'),
            'mp3_audio_player_pro_settings',
            'mp3_player_general'
        );
        
        add_settings_field(
            'autoplay',
            __('Autoplay', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'autoplay_field_callback'),
            'mp3_audio_player_pro_settings',
            'mp3_player_general'
        );
        
        add_settings_field(
            'default_volume',
            __('Default Volume', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'volume_field_callback'),
            'mp3_audio_player_pro_settings',
            'mp3_player_general'
        );
        
        // Appearance settings
        add_settings_field(
            'show_waveform',
            __('Show Waveform', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'waveform_field_callback'),
            'mp3_audio_player_pro_settings',
            'mp3_player_appearance'
        );
        
        add_settings_field(
            'sticky_player',
            __('Sticky Player', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'sticky_field_callback'),
            'mp3_audio_player_pro_settings',
            'mp3_player_appearance'
        );
        
        add_settings_field(
            'custom_css',
            __('Custom CSS', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'custom_css_field_callback'),
            'mp3_audio_player_pro_settings',
            'mp3_player_appearance'
        );
        
        // Advanced settings
        add_settings_field(
            'load_everywhere',
            __('Load Assets Everywhere', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'load_everywhere_field_callback'),
            'mp3_audio_player_pro_settings',
            'mp3_player_advanced'
        );
        
        add_settings_field(
            'enable_analytics',
            __('Enable Analytics', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
            array($this, 'analytics_field_callback'),
            'mp3_audio_player_pro_settings',
            'mp3_player_advanced'
        );
    }
    
    /**
     * Section callbacks
     */
    public function general_section_callback() {
        echo '<p>' . __('Configure general player settings.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN) . '</p>';
    }
    
    public function appearance_section_callback() {
        echo '<p>' . __('Customize the appearance of your audio player.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN) . '</p>';
    }
    
    public function advanced_section_callback() {
        echo '<p>' . __('Advanced configuration options.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN) . '</p>';
    }
    
    /**
     * Field callbacks
     */
    public function theme_field_callback() {
        $settings = get_option('mp3_audio_player_pro_settings', array());
        $value = isset($settings['default_theme']) ? $settings['default_theme'] : 'light';
        ?>
        <select name="mp3_audio_player_pro_settings[default_theme]">
            <option value="light" <?php selected($value, 'light'); ?>><?php _e('Light', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
            <option value="dark" <?php selected($value, 'dark'); ?>><?php _e('Dark', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
        </select>
        <p class="description"><?php _e('Default theme for new players.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
        <?php
    }
    
    public function autoplay_field_callback() {
        $settings = get_option('mp3_audio_player_pro_settings', array());
        $value = isset($settings['autoplay']) ? $settings['autoplay'] : false;
        ?>
        <label>
            <input type="checkbox" name="mp3_audio_player_pro_settings[autoplay]" value="1" <?php checked($value, true); ?>>
            <?php _e('Enable autoplay by default', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
        </label>
        <p class="description"><?php _e('Note: Most browsers block autoplay with sound.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
        <?php
    }
    
    public function volume_field_callback() {
        $settings = get_option('mp3_audio_player_pro_settings', array());
        $value = isset($settings['default_volume']) ? $settings['default_volume'] : 1;
        ?>
        <input type="range" name="mp3_audio_player_pro_settings[default_volume]" 
               min="0" max="1" step="0.1" value="<?php echo esc_attr($value); ?>" 
               oninput="this.nextElementSibling.value = Math.round(this.value * 100) + '%'">
        <output><?php echo round($value * 100); ?>%</output>
        <p class="description"><?php _e('Default volume level (0-100%).', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
        <?php
    }
    
    public function waveform_field_callback() {
        $settings = get_option('mp3_audio_player_pro_settings', array());
        $value = isset($settings['show_waveform']) ? $settings['show_waveform'] : true;
        ?>
        <label>
            <input type="checkbox" name="mp3_audio_player_pro_settings[show_waveform]" value="1" <?php checked($value, true); ?>>
            <?php _e('Show waveform visualization by default', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
        </label>
        <?php
    }
    
    public function sticky_field_callback() {
        $settings = get_option('mp3_audio_player_pro_settings', array());
        $value = isset($settings['sticky_player']) ? $settings['sticky_player'] : true;
        ?>
        <label>
            <input type="checkbox" name="mp3_audio_player_pro_settings[sticky_player]" value="1" <?php checked($value, true); ?>>
            <?php _e('Enable sticky player by default', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
        </label>
        <?php
    }
    
    public function custom_css_field_callback() {
        $settings = get_option('mp3_audio_player_pro_settings', array());
        $value = isset($settings['custom_css']) ? $settings['custom_css'] : '';
        ?>
        <textarea name="mp3_audio_player_pro_settings[custom_css]" rows="10" cols="50" class="large-text code"><?php echo esc_textarea($value); ?></textarea>
        <p class="description"><?php _e('Add custom CSS to style your players.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
        <?php
    }
    
    public function load_everywhere_field_callback() {
        $settings = get_option('mp3_audio_player_pro_settings', array());
        $value = isset($settings['load_everywhere']) ? $settings['load_everywhere'] : false;
        ?>
        <label>
            <input type="checkbox" name="mp3_audio_player_pro_settings[load_everywhere]" value="1" <?php checked($value, true); ?>>
            <?php _e('Load player assets on all pages', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
        </label>
        <p class="description"><?php _e('Enable if you have players in widgets or theme files.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></p>
        <?php
    }
    
    public function analytics_field_callback() {
        $settings = get_option('mp3_audio_player_pro_settings', array());
        $value = isset($settings['enable_analytics']) ? $settings['enable_analytics'] : false;
        ?>
        <label>
            <input type="checkbox" name="mp3_audio_player_pro_settings[enable_analytics]" value="1" <?php checked($value, true); ?>>
            <?php _e('Enable play/pause analytics tracking', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
        </label>
        <?php
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();
        
        if (isset($input['default_theme'])) {
            $sanitized['default_theme'] = in_array($input['default_theme'], array('light', 'dark')) ? $input['default_theme'] : 'light';
        }
        
        if (isset($input['autoplay'])) {
            $sanitized['autoplay'] = (bool) $input['autoplay'];
        }
        
        if (isset($input['default_volume'])) {
            $sanitized['default_volume'] = max(0, min(1, floatval($input['default_volume'])));
        }
        
        if (isset($input['show_waveform'])) {
            $sanitized['show_waveform'] = (bool) $input['show_waveform'];
        }
        
        if (isset($input['sticky_player'])) {
            $sanitized['sticky_player'] = (bool) $input['sticky_player'];
        }
        
        if (isset($input['custom_css'])) {
            $sanitized['custom_css'] = wp_strip_all_tags($input['custom_css']);
        }
        
        if (isset($input['load_everywhere'])) {
            $sanitized['load_everywhere'] = (bool) $input['load_everywhere'];
        }
        
        if (isset($input['enable_analytics'])) {
            $sanitized['enable_analytics'] = (bool) $input['enable_analytics'];
        }
        
        return $sanitized;
    }
    
    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        $post_types = array('post', 'page');
        
        foreach ($post_types as $post_type) {
            add_meta_box(
                'mp3-audio-player-meta',
                __('MP3 Audio Player', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                array($this, 'meta_box_callback'),
                $post_type,
                'side',
                'default'
            );
        }
    }
    
    /**
     * Meta box callback
     */
    public function meta_box_callback($post) {
        wp_nonce_field('mp3_audio_player_meta_nonce', 'mp3_audio_player_meta_nonce');
        
        $playlist_id = get_post_meta($post->ID, '_mp3_audio_player_playlist', true);
        $auto_insert = get_post_meta($post->ID, '_mp3_audio_player_auto_insert', true);
        
        // Get playlists
        $db = MP3_Audio_Player_Pro_Database::get_instance();
        $playlists = $db->get_playlists();
        ?>
        <p>
            <label for="mp3_audio_player_playlist"><?php _e('Default Playlist:', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></label>
            <select name="mp3_audio_player_playlist" id="mp3_audio_player_playlist" style="width: 100%;">
                <option value=""><?php _e('None', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?></option>
                <?php foreach ($playlists as $playlist): ?>
                    <option value="<?php echo esc_attr($playlist->id); ?>" <?php selected($playlist_id, $playlist->id); ?>>
                        <?php echo esc_html($playlist->name); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </p>
        
        <p>
            <label>
                <input type="checkbox" name="mp3_audio_player_auto_insert" value="1" <?php checked($auto_insert, '1'); ?>>
                <?php _e('Auto-insert player at the end of content', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
            </label>
        </p>
        
        <p>
            <button type="button" class="button" onclick="mp3PlayerInsertShortcode()">
                <?php _e('Insert Shortcode', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN); ?>
            </button>
        </p>
        
        <script>
        function mp3PlayerInsertShortcode() {
            var playlistId = document.getElementById('mp3_audio_player_playlist').value;
            var shortcode = '[mp3_audio_player';
            
            if (playlistId) {
                shortcode += ' playlist="' + playlistId + '"';
            }
            
            shortcode += ']';
            
            // Insert into editor
            if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor && !tinyMCE.activeEditor.isHidden()) {
                tinyMCE.activeEditor.insertContent(shortcode);
            } else {
                var textarea = document.getElementById('content');
                if (textarea) {
                    textarea.value += shortcode;
                }
            }
        }
        </script>
        <?php
    }
    
    /**
     * Save post meta
     */
    public function save_post_meta($post_id) {
        if (!isset($_POST['mp3_audio_player_meta_nonce']) || 
            !wp_verify_nonce($_POST['mp3_audio_player_meta_nonce'], 'mp3_audio_player_meta_nonce')) {
            return;
        }
        
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        if (isset($_POST['mp3_audio_player_playlist'])) {
            update_post_meta($post_id, '_mp3_audio_player_playlist', sanitize_text_field($_POST['mp3_audio_player_playlist']));
        }
        
        if (isset($_POST['mp3_audio_player_auto_insert'])) {
            update_post_meta($post_id, '_mp3_audio_player_auto_insert', '1');
        } else {
            delete_post_meta($post_id, '_mp3_audio_player_auto_insert');
        }
    }
    
    /**
     * Add media button
     */
    public function add_media_button() {
        echo '<button type="button" class="button" onclick="mp3PlayerOpenModal()">';
        echo '<span class="dashicons dashicons-format-audio" style="vertical-align: middle;"></span> ';
        echo __('Add Audio Player', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN);
        echo '</button>';
    }
    
    /**
     * Add TinyMCE plugin
     */
    public function add_tinymce_plugin($plugins) {
        $plugins['mp3_audio_player'] = MP3_AUDIO_PLAYER_PRO_PLUGIN_URL . 'assets/js/tinymce-plugin.js';
        return $plugins;
    }
    
    /**
     * Add TinyMCE button
     */
    public function add_tinymce_button($buttons) {
        array_push($buttons, 'mp3_audio_player');
        return $buttons;
    }
    
    /**
     * Admin notices
     */
    public function admin_notices() {
        // Check for required PHP version
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            ?>
            <div class="notice notice-error">
                <p>
                    <?php printf(
                        __('MP3 Audio Player Pro requires PHP 7.4 or higher. You are running PHP %s.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                        PHP_VERSION
                    ); ?>
                </p>
            </div>
            <?php
        }
        
        // Check for required WordPress version
        if (version_compare(get_bloginfo('version'), '5.0', '<')) {
            ?>
            <div class="notice notice-error">
                <p>
                    <?php printf(
                        __('MP3 Audio Player Pro requires WordPress 5.0 or higher. You are running WordPress %s.', MP3_AUDIO_PLAYER_PRO_TEXT_DOMAIN),
                        get_bloginfo('version')
                    ); ?>
                </p>
            </div>
            <?php
        }
    }
}
