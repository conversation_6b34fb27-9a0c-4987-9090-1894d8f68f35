<?php
/**
 * Database Management Class
 *
 * @package MP3AudioPlayerPro
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * MP3 Audio Player Pro Database Class
 */
class MP3_Audio_Player_Pro_Database {
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Table names
     */
    private $playlists_table;
    private $tracks_table;
    private $playlist_tracks_table;
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        global $wpdb;
        
        $this->playlists_table = $wpdb->prefix . 'mp3_player_playlists';
        $this->tracks_table = $wpdb->prefix . 'mp3_player_tracks';
        $this->playlist_tracks_table = $wpdb->prefix . 'mp3_player_playlist_tracks';
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Playlists table
        $playlists_sql = "CREATE TABLE {$this->playlists_table} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            user_id bigint(20) unsigned NOT NULL,
            is_public tinyint(1) DEFAULT 0,
            settings longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY is_public (is_public)
        ) $charset_collate;";
        
        // Tracks table
        $tracks_sql = "CREATE TABLE {$this->tracks_table} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            artist varchar(255) DEFAULT '',
            album varchar(255) DEFAULT '',
            duration int(11) DEFAULT 0,
            file_url varchar(500) NOT NULL,
            file_path varchar(500) DEFAULT '',
            attachment_id bigint(20) unsigned DEFAULT NULL,
            artwork_url varchar(500) DEFAULT '',
            genre varchar(100) DEFAULT '',
            year varchar(4) DEFAULT '',
            track_number varchar(10) DEFAULT '',
            file_size bigint(20) DEFAULT 0,
            file_format varchar(20) DEFAULT '',
            bitrate varchar(20) DEFAULT '',
            sample_rate varchar(20) DEFAULT '',
            metadata longtext,
            user_id bigint(20) unsigned NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY attachment_id (attachment_id),
            KEY artist (artist),
            KEY album (album)
        ) $charset_collate;";
        
        // Playlist tracks relationship table
        $playlist_tracks_sql = "CREATE TABLE {$this->playlist_tracks_table} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            playlist_id bigint(20) unsigned NOT NULL,
            track_id bigint(20) unsigned NOT NULL,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY playlist_track (playlist_id, track_id),
            KEY playlist_id (playlist_id),
            KEY track_id (track_id),
            KEY sort_order (sort_order),
            FOREIGN KEY (playlist_id) REFERENCES {$this->playlists_table}(id) ON DELETE CASCADE,
            FOREIGN KEY (track_id) REFERENCES {$this->tracks_table}(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        dbDelta($playlists_sql);
        dbDelta($tracks_sql);
        dbDelta($playlist_tracks_sql);
    }
    
    /**
     * Drop database tables
     */
    public function drop_tables() {
        global $wpdb;
        
        $wpdb->query("DROP TABLE IF EXISTS {$this->playlist_tracks_table}");
        $wpdb->query("DROP TABLE IF EXISTS {$this->tracks_table}");
        $wpdb->query("DROP TABLE IF EXISTS {$this->playlists_table}");
    }
    
    /**
     * Get playlists
     */
    public function get_playlists($user_id = null, $public_only = false) {
        global $wpdb;
        
        $where_clauses = array();
        $where_values = array();
        
        if ($user_id !== null) {
            $where_clauses[] = 'user_id = %d';
            $where_values[] = $user_id;
        }
        
        if ($public_only) {
            $where_clauses[] = 'is_public = 1';
        }
        
        $where_sql = '';
        if (!empty($where_clauses)) {
            $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);
        }
        
        $sql = "SELECT * FROM {$this->playlists_table} {$where_sql} ORDER BY updated_at DESC";
        
        if (!empty($where_values)) {
            $sql = $wpdb->prepare($sql, $where_values);
        }
        
        return $wpdb->get_results($sql);
    }
    
    /**
     * Get playlist by ID
     */
    public function get_playlist($id) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->playlists_table} WHERE id = %d",
            $id
        );
        
        return $wpdb->get_row($sql);
    }
    
    /**
     * Create playlist
     */
    public function create_playlist($data) {
        global $wpdb;
        
        $defaults = array(
            'name' => '',
            'description' => '',
            'user_id' => get_current_user_id(),
            'is_public' => 0,
            'settings' => '',
        );
        
        $data = wp_parse_args($data, $defaults);
        
        // Serialize settings if it's an array
        if (is_array($data['settings'])) {
            $data['settings'] = serialize($data['settings']);
        }
        
        $result = $wpdb->insert(
            $this->playlists_table,
            $data,
            array('%s', '%s', '%d', '%d', '%s')
        );
        
        if ($result !== false) {
            return $wpdb->insert_id;
        }
        
        return false;
    }
    
    /**
     * Update playlist
     */
    public function update_playlist($id, $data) {
        global $wpdb;
        
        // Serialize settings if it's an array
        if (isset($data['settings']) && is_array($data['settings'])) {
            $data['settings'] = serialize($data['settings']);
        }
        
        $result = $wpdb->update(
            $this->playlists_table,
            $data,
            array('id' => $id),
            null,
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Delete playlist
     */
    public function delete_playlist($id) {
        global $wpdb;
        
        // Delete playlist tracks first (foreign key constraint)
        $wpdb->delete(
            $this->playlist_tracks_table,
            array('playlist_id' => $id),
            array('%d')
        );
        
        // Delete playlist
        $result = $wpdb->delete(
            $this->playlists_table,
            array('id' => $id),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Get tracks
     */
    public function get_tracks($user_id = null, $playlist_id = null) {
        global $wpdb;
        
        if ($playlist_id) {
            // Get tracks for specific playlist
            $sql = "SELECT t.*, pt.sort_order 
                    FROM {$this->tracks_table} t
                    INNER JOIN {$this->playlist_tracks_table} pt ON t.id = pt.track_id
                    WHERE pt.playlist_id = %d
                    ORDER BY pt.sort_order ASC, t.created_at DESC";
            
            $sql = $wpdb->prepare($sql, $playlist_id);
        } else {
            // Get all tracks for user
            $where_sql = '';
            if ($user_id !== null) {
                $where_sql = $wpdb->prepare('WHERE user_id = %d', $user_id);
            }
            
            $sql = "SELECT * FROM {$this->tracks_table} {$where_sql} ORDER BY created_at DESC";
        }
        
        return $wpdb->get_results($sql);
    }
    
    /**
     * Get track by ID
     */
    public function get_track($id) {
        global $wpdb;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM {$this->tracks_table} WHERE id = %d",
            $id
        );
        
        return $wpdb->get_row($sql);
    }
    
    /**
     * Create track
     */
    public function create_track($data) {
        global $wpdb;
        
        $defaults = array(
            'title' => '',
            'artist' => '',
            'album' => '',
            'duration' => 0,
            'file_url' => '',
            'file_path' => '',
            'attachment_id' => null,
            'artwork_url' => '',
            'genre' => '',
            'year' => '',
            'track_number' => '',
            'file_size' => 0,
            'file_format' => '',
            'bitrate' => '',
            'sample_rate' => '',
            'metadata' => '',
            'user_id' => get_current_user_id(),
        );
        
        $data = wp_parse_args($data, $defaults);
        
        // Serialize metadata if it's an array
        if (is_array($data['metadata'])) {
            $data['metadata'] = serialize($data['metadata']);
        }
        
        $result = $wpdb->insert(
            $this->tracks_table,
            $data,
            array('%s', '%s', '%s', '%d', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s', '%d')
        );
        
        if ($result !== false) {
            return $wpdb->insert_id;
        }
        
        return false;
    }
    
    /**
     * Update track
     */
    public function update_track($id, $data) {
        global $wpdb;
        
        // Serialize metadata if it's an array
        if (isset($data['metadata']) && is_array($data['metadata'])) {
            $data['metadata'] = serialize($data['metadata']);
        }
        
        $result = $wpdb->update(
            $this->tracks_table,
            $data,
            array('id' => $id),
            null,
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Delete track
     */
    public function delete_track($id) {
        global $wpdb;
        
        // Delete from playlists first
        $wpdb->delete(
            $this->playlist_tracks_table,
            array('track_id' => $id),
            array('%d')
        );
        
        // Delete track
        $result = $wpdb->delete(
            $this->tracks_table,
            array('id' => $id),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Add track to playlist
     */
    public function add_track_to_playlist($playlist_id, $track_id, $sort_order = 0) {
        global $wpdb;
        
        // Get next sort order if not provided
        if ($sort_order === 0) {
            $max_order = $wpdb->get_var($wpdb->prepare(
                "SELECT MAX(sort_order) FROM {$this->playlist_tracks_table} WHERE playlist_id = %d",
                $playlist_id
            ));
            $sort_order = ($max_order !== null) ? $max_order + 1 : 1;
        }
        
        $result = $wpdb->insert(
            $this->playlist_tracks_table,
            array(
                'playlist_id' => $playlist_id,
                'track_id' => $track_id,
                'sort_order' => $sort_order,
            ),
            array('%d', '%d', '%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Remove track from playlist
     */
    public function remove_track_from_playlist($playlist_id, $track_id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->playlist_tracks_table,
            array(
                'playlist_id' => $playlist_id,
                'track_id' => $track_id,
            ),
            array('%d', '%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Update playlist track order
     */
    public function update_playlist_track_order($playlist_id, $track_orders) {
        global $wpdb;
        
        foreach ($track_orders as $track_id => $sort_order) {
            $wpdb->update(
                $this->playlist_tracks_table,
                array('sort_order' => $sort_order),
                array(
                    'playlist_id' => $playlist_id,
                    'track_id' => $track_id,
                ),
                array('%d'),
                array('%d', '%d')
            );
        }
        
        return true;
    }
    
    /**
     * Get table names
     */
    public function get_table_names() {
        return array(
            'playlists' => $this->playlists_table,
            'tracks' => $this->tracks_table,
            'playlist_tracks' => $this->playlist_tracks_table,
        );
    }
}
