# MP3 Audio Player

A professional, feature-rich MP3 audio player inspired by the Sonaar MP3 Audio Player for WordPress. Built with modern web technologies including HTML5, CSS3, and vanilla JavaScript.

![MP3 Audio Player](https://img.shields.io/badge/version-1.0.0-blue.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

## 🎵 Features

### Core Audio Features
- **High-Quality Audio Playback** - HTML5 Audio API with Web Audio API integration
- **Multiple Format Support** - MP3, WAV, OGG, M4A, AAC, FLAC
- **Interactive Waveform** - Visual waveform display with click-to-seek functionality
- **Advanced Controls** - Play, pause, skip, shuffle, repeat, speed control
- **Volume Control** - Precise volume adjustment with mute functionality

### Playlist Management
- **Drag & Drop Upload** - Easy file upload with drag-and-drop support
- **Playlist Organization** - Add, remove, and reorder tracks
- **Search & Filter** - Real-time search and sorting capabilities
- **Metadata Extraction** - Automatic ID3 tag reading for track information

### User Interface
- **Modern Design** - Clean, responsive interface inspired by Sonaar
- **Dark/Light Themes** - Toggle between light and dark modes
- **Sticky Player** - Persistent mini-player for continuous playback
- **Responsive Layout** - Works perfectly on desktop, tablet, and mobile
- **Keyboard Shortcuts** - Full keyboard control support

### Advanced Features
- **Continuous Playback** - Seamless playback across page navigation
- **Adaptive Colors** - UI colors adapt to album artwork
- **Audio Visualization** - Real-time frequency analysis and visualization
- **Playback Speed Control** - Variable speed from 0.5x to 2x
- **Media Session API** - Integration with browser media controls
- **Local Storage** - Saves preferences and playlist state

## 🚀 Quick Start

1. **Clone or Download** the project files
2. **Open `index.html`** in a modern web browser
3. **Upload Music** using the upload button or drag & drop
4. **Enjoy!** Your music with professional-grade controls

### Browser Requirements
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 📁 Project Structure

```
mp3-player/
├── index.html              # Main HTML file
├── css/
│   └── style.css          # Complete stylesheet with themes
├── js/
│   ├── audio-engine.js    # Core audio functionality
│   ├── playlist-manager.js # Playlist operations
│   ├── waveform.js        # Waveform visualization
│   ├── ui-controller.js   # UI management
│   ├── file-manager.js    # File upload & processing
│   └── app.js             # Main application
├── assets/
│   └── images/
│       └── default-album.jpg # Default album artwork
└── README.md              # This file
```

## 🎮 Controls & Shortcuts

### Mouse Controls
- **Click waveform** - Seek to position
- **Drag progress bar** - Scrub through track
- **Double-click playlist item** - Play track
- **Right-click track** - Context menu (future feature)

### Keyboard Shortcuts
- **Spacebar** - Play/Pause
- **←/→ Arrow Keys** - Seek backward/forward (10s)
- **↑/↓ Arrow Keys** - Volume up/down
- **M** - Toggle mute
- **S** - Toggle shuffle
- **R** - Cycle repeat modes
- **Ctrl/Cmd + N** - Next track
- **Ctrl/Cmd + P** - Previous track
- **Escape** - Close modals

## 🎨 Customization

### Themes
The player supports light and dark themes. You can customize colors by modifying CSS custom properties in `css/style.css`:

```css
:root {
    --primary-color: #6366f1;
    --bg-primary: #ffffff;
    --text-primary: #1e293b;
    /* ... more variables */
}
```

### Adding Custom Features
The modular architecture makes it easy to extend:

1. **Audio Engine** (`js/audio-engine.js`) - Core playback functionality
2. **Playlist Manager** (`js/playlist-manager.js`) - Track management
3. **UI Controller** (`js/ui-controller.js`) - Interface interactions
4. **Waveform Visualizer** (`js/waveform.js`) - Visual components

## 🔧 Technical Details

### Architecture
- **Modular Design** - Separate classes for different concerns
- **Event-Driven** - Components communicate via custom events
- **Progressive Enhancement** - Works without JavaScript (basic HTML5 audio)
- **Memory Efficient** - Proper cleanup and resource management

### Audio Processing
- **Web Audio API** - For advanced audio analysis and effects
- **HTML5 Audio** - For reliable cross-browser playback
- **Real-time Analysis** - Frequency and time-domain data for visualizations
- **Gapless Playback** - Smooth transitions between tracks

### Performance
- **Lazy Loading** - Waveforms generated on-demand
- **Efficient Rendering** - Canvas-based visualizations with requestAnimationFrame
- **Memory Management** - Automatic cleanup of object URLs and audio contexts
- **Responsive Design** - CSS Grid and Flexbox for optimal layouts

## 🌟 Features Comparison with Sonaar

| Feature | Our Player | Sonaar MP3 Player |
|---------|------------|-------------------|
| Waveform Visualization | ✅ Interactive | ✅ Interactive |
| Drag & Drop Upload | ✅ Yes | ✅ Yes |
| Playlist Management | ✅ Full Featured | ✅ Full Featured |
| Sticky Player | ✅ Yes | ✅ Yes |
| Dark/Light Themes | ✅ Yes | ✅ Yes |
| Keyboard Shortcuts | ✅ Comprehensive | ✅ Basic |
| Speed Control | ✅ 0.5x - 2x | ✅ Limited |
| Audio Visualization | ✅ Real-time | ✅ Static |
| Mobile Responsive | ✅ Fully Responsive | ✅ Responsive |
| WordPress Integration | ❌ Standalone | ✅ WordPress Plugin |

## 🐛 Known Issues & Limitations

1. **File Storage** - Files are stored in browser memory (not persistent)
2. **Large Files** - Very large audio files may impact performance
3. **Safari Limitations** - Some Web Audio API features limited on iOS Safari
4. **ID3 Tag Reading** - Basic implementation (consider using jsmediatags library)

## 🔮 Future Enhancements

- [ ] Equalizer with preset and custom settings
- [ ] Crossfade between tracks
- [ ] Playlist import/export (M3U, PLS formats)
- [ ] Cloud storage integration (Google Drive, Dropbox)
- [ ] Social sharing features
- [ ] Audio effects (reverb, echo, etc.)
- [ ] Lyrics display and synchronization
- [ ] Podcast-specific features
- [ ] PWA (Progressive Web App) support
- [ ] Offline playback capabilities

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request. For major changes, please open an issue first to discuss what you would like to change.

## 🙏 Acknowledgments

- Inspired by [Sonaar MP3 Audio Player](https://sonaar.io/mp3-audio-player-pro/)
- Icons by [Font Awesome](https://fontawesome.com/)
- Fonts by [Google Fonts](https://fonts.google.com/)

## 📞 Support

If you encounter any issues or have questions:

1. Check the [Issues](../../issues) page
2. Create a new issue with detailed information
3. Include browser version and console errors if applicable

---

**Made with ❤️ for music lovers everywhere**
