# WordPress Plugin Installation Guide

This guide will help you convert the standalone MP3 Audio Player into a WordPress plugin and install it on your WordPress site.

## 📦 Plugin Package Structure

The WordPress plugin version includes all the necessary files structured according to WordPress standards:

```
mp3-audio-player-pro/
├── mp3-audio-player-pro.php    # Main plugin file
├── includes/                   # Core functionality
│   ├── class-database.php      # Database management
│   ├── class-admin.php         # Admin interface
│   ├── class-shortcode.php     # Shortcode handler
│   ├── class-widget.php        # Widget functionality
│   ├── class-rest-api.php      # REST API endpoints
│   ├── class-file-handler.php  # File upload handling
│   └── functions.php           # Helper functions
├── admin/                      # Admin interface
│   └── views/                  # Admin page templates
│       ├── main.php            # Dashboard
│       ├── playlists.php       # Playlist management
│       └── settings.php        # Plugin settings
├── assets/                     # Frontend assets
│   ├── css/                    # Stylesheets
│   ├── js/                     # JavaScript files
│   └── images/                 # Images and icons
├── languages/                  # Translation files (optional)
├── README.md                   # Plugin documentation
└── WORDPRESS-INSTALLATION.md   # This file
```

## 🚀 Installation Methods

### Method 1: Manual Installation (Recommended)

1. **Prepare the Plugin Package**
   ```bash
   # Create plugin directory
   mkdir mp3-audio-player-pro
   
   # Copy all plugin files to the directory
   # (All files from this project)
   ```

2. **Upload to WordPress**
   - Compress the `mp3-audio-player-pro` folder into a ZIP file
   - Go to your WordPress admin dashboard
   - Navigate to **Plugins > Add New > Upload Plugin**
   - Choose the ZIP file and click **Install Now**
   - Click **Activate Plugin**

### Method 2: FTP Upload

1. **Upload via FTP**
   - Upload the `mp3-audio-player-pro` folder to `/wp-content/plugins/`
   - Go to **Plugins** in your WordPress admin
   - Find "MP3 Audio Player Pro" and click **Activate**

### Method 3: Direct Server Access

1. **Server Upload**
   ```bash
   # Navigate to WordPress plugins directory
   cd /path/to/wordpress/wp-content/plugins/
   
   # Upload and extract plugin files
   # Ensure proper file permissions (755 for directories, 644 for files)
   ```

## ⚙️ Post-Installation Setup

### 1. Initial Configuration

After activation, the plugin will:
- Create necessary database tables
- Set default settings
- Create upload directories with proper permissions

### 2. Plugin Settings

Navigate to **MP3 Player > Settings** to configure:

- **Default Theme**: Light or Dark
- **Autoplay**: Enable/disable autoplay (note browser restrictions)
- **Default Volume**: Set initial volume level
- **Waveform Display**: Show/hide waveform visualization
- **Sticky Player**: Enable persistent mini-player
- **File Upload Limits**: Maximum file size and allowed formats
- **Custom CSS**: Add custom styling

### 3. Upload Audio Files

**Option A: WordPress Media Library**
- Go to **Media > Add New**
- Upload your audio files (MP3, WAV, OGG, M4A, AAC, FLAC)
- Files will be automatically detected by the plugin

**Option B: Plugin Upload Interface**
- Go to **MP3 Player > Playlists**
- Click **Add New** to create a playlist
- Use the built-in upload interface

### 4. Create Playlists

1. Navigate to **MP3 Player > Playlists**
2. Click **Add New**
3. Enter playlist name and description
4. Select tracks to include
5. Set visibility (public/private)
6. Save the playlist

## 🎵 Using the Player

### Shortcode Usage

The plugin provides a flexible shortcode system:

```php
// Basic player with all tracks
[mp3_audio_player]

// Player with specific playlist
[mp3_audio_player playlist="123"]

// Compact layout with dark theme
[mp3_audio_player layout="compact" theme="dark"]

// Mini player without playlist
[mp3_audio_player layout="mini" show_playlist="false"]

// Custom configuration
[mp3_audio_player 
    playlist="123" 
    theme="dark" 
    layout="default" 
    show_waveform="true" 
    show_playlist="true" 
    sticky_player="true" 
    autoplay="false" 
    volume="0.8"]
```

### Shortcode Parameters

| Parameter | Options | Default | Description |
|-----------|---------|---------|-------------|
| `playlist` | Playlist ID | All tracks | Specific playlist to display |
| `tracks` | Track IDs (comma-separated) | All tracks | Specific tracks to display |
| `theme` | `light`, `dark` | `light` | Player color scheme |
| `layout` | `default`, `compact`, `mini` | `default` | Player layout style |
| `show_waveform` | `true`, `false` | `true` | Display waveform visualization |
| `show_playlist` | `true`, `false` | `true` | Display playlist panel |
| `sticky_player` | `true`, `false` | `true` | Enable sticky footer player |
| `autoplay` | `true`, `false` | `false` | Auto-start playback |
| `volume` | 0.0 - 1.0 | 1.0 | Initial volume level |
| `width` | CSS value | `100%` | Player width |
| `height` | CSS value | `auto` | Player height |

### Widget Usage

1. Go to **Appearance > Widgets**
2. Find "MP3 Audio Player" widget
3. Drag to desired widget area
4. Configure widget settings:
   - Title
   - Playlist selection
   - Layout (mini, compact, default)
   - Theme
   - Display options

### Template Integration

For theme developers, you can integrate the player directly:

```php
// Display player in theme templates
echo do_shortcode('[mp3_audio_player playlist="123"]');

// Or use the function directly
if (function_exists('mp3_audio_player_pro_get_shortcode')) {
    echo do_shortcode(mp3_audio_player_pro_get_shortcode([
        'playlist' => 123,
        'theme' => 'dark',
        'layout' => 'compact'
    ]));
}
```

## 🔧 Advanced Configuration

### Custom CSS

Add custom styling in **MP3 Player > Settings > Custom CSS**:

```css
/* Custom player styling */
.mp3-audio-player-container {
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Custom color scheme */
.mp3-audio-player-container.theme-custom {
    --primary-color: #ff6b6b;
    --bg-primary: #f8f9fa;
}
```

### Hooks and Filters

The plugin provides several hooks for customization:

```php
// Modify supported file formats
add_filter('mp3_audio_player_pro_supported_formats', function($formats) {
    $formats['opus'] = array(
        'mime' => 'audio/opus',
        'name' => 'OPUS'
    );
    return $formats;
});

// Modify maximum file size
add_filter('mp3_player_max_file_size', function($size) {
    return 100 * 1024 * 1024; // 100MB
});

// Custom upload directory
add_filter('mp3_audio_player_upload_dir', function($dir) {
    $dir['subdir'] = '/custom-audio' . $dir['subdir'];
    return $dir;
});

// Log player events
add_action('mp3_audio_player_pro_log_event', function($event_type, $track_id, $user_id) {
    error_log("Player event: {$event_type} for track {$track_id} by user {$user_id}");
});
```

### REST API

The plugin provides REST API endpoints:

```javascript
// Get playlists
fetch('/wp-json/mp3-audio-player-pro/v1/playlists')

// Get tracks
fetch('/wp-json/mp3-audio-player-pro/v1/tracks')

// Get specific playlist
fetch('/wp-json/mp3-audio-player-pro/v1/playlists/123')
```

## 🛠️ Troubleshooting

### Common Issues

1. **Files not uploading**
   - Check file size limits in WordPress and server
   - Verify file permissions on upload directory
   - Ensure supported file formats

2. **Player not displaying**
   - Check if shortcode is correct
   - Verify plugin is activated
   - Check for JavaScript errors in browser console

3. **Waveform not generating**
   - Ensure Web Audio API support in browser
   - Check file format compatibility
   - Verify server has sufficient memory

4. **Database errors**
   - Check WordPress database permissions
   - Verify table creation during activation
   - Check error logs for specific issues

### Debug Mode

Enable WordPress debug mode to troubleshoot:

```php
// In wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Performance Optimization

1. **Caching**
   - Use caching plugins compatible with dynamic content
   - Consider CDN for audio file delivery

2. **File Optimization**
   - Compress audio files appropriately
   - Use appropriate bitrates for web delivery

3. **Server Configuration**
   - Increase PHP memory limit if needed
   - Optimize server for audio file delivery

## 📞 Support

For support and updates:

1. Check the plugin documentation
2. Review WordPress error logs
3. Test with default WordPress theme
4. Disable other plugins to check for conflicts

## 🔄 Updates

The plugin includes an update checker. Future versions will:
- Maintain backward compatibility
- Preserve custom settings
- Migrate database schema if needed

## 📄 License

This plugin is released under the MIT License, making it free to use, modify, and distribute.

---

**Enjoy your professional MP3 audio player on WordPress!** 🎵
